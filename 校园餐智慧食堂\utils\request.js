// API请求封装模块
const BASE_URL = 'https://your-backend-domain.com/api';

// 请求拦截器
const httpInterceptor = {
  invoke(options) {
    // 设置基础URL
    if (!options.url.startsWith('http')) {
      options.url = BASE_URL + options.url;
    }
    
    // 设置请求头
    options.header = {
      'Content-Type': 'application/json',
      ...options.header
    };
    
    // 添加token
    const token = uni.getStorageSync('token');
    if (token) {
      options.header.Authorization = `Bearer ${token}`;
    }
    
    // 添加时间戳防止缓存
    if (options.method === 'GET') {
      const connector = options.url.includes('?') ? '&' : '?';
      options.url += `${connector}_t=${Date.now()}`;
    }
  }
};

// 响应拦截器
const responseInterceptor = {
  invoke(response) {
    const { statusCode, data } = response;
    
    // 处理HTTP状态码
    if (statusCode >= 200 && statusCode < 300) {
      return response;
    } else if (statusCode === 401) {
      // token过期或无效，清除本地存储并跳转登录
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      uni.reLaunch({
        url: '/pages/login/index'
      });
      return Promise.reject(new Error('登录已过期，请重新登录'));
    } else if (statusCode === 403) {
      return Promise.reject(new Error('没有权限访问'));
    } else if (statusCode === 404) {
      return Promise.reject(new Error('请求的资源不存在'));
    } else if (statusCode >= 500) {
      return Promise.reject(new Error('服务器内部错误'));
    } else {
      return Promise.reject(new Error(data.message || '请求失败'));
    }
  }
};

// 注册拦截器
uni.addInterceptor('request', httpInterceptor);
uni.addInterceptor('uploadFile', httpInterceptor);

// 封装请求方法
export const request = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      success: (res) => {
        try {
          const processedRes = responseInterceptor.invoke(res);
          if (processedRes && processedRes.data) {
            resolve(processedRes.data);
          } else {
            resolve(res.data);
          }
        } catch (error) {
          reject(error);
        }
      },
      fail: (err) => {
        console.error('网络请求失败:', err);
        reject(new Error('网络连接失败，请检查网络设置'));
      }
    });
  });
};

// GET请求
export const get = (url, params = {}) => {
  return request({
    url,
    method: 'GET',
    data: params
  });
};

// POST请求
export const post = (url, data = {}) => {
  return request({
    url,
    method: 'POST',
    data
  });
};

// PUT请求
export const put = (url, data = {}) => {
  return request({
    url,
    method: 'PUT',
    data
  });
};

// DELETE请求
export const del = (url, data = {}) => {
  return request({
    url,
    method: 'DELETE',
    data
  });
};

// 文件上传（带进度）
export const upload = (url, filePath, formData = {}, onProgress = null) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token');

    const uploadTask = uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name: 'file',
      formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          if (res.statusCode === 200) {
            resolve(data);
          } else {
            reject(new Error(data.message || '上传失败'));
          }
        } catch (error) {
          reject(new Error('上传响应解析失败'));
        }
      },
      fail: (err) => {
        console.error('文件上传失败:', err);
        reject(new Error('文件上传失败'));
      }
    });

    // 监听上传进度
    if (onProgress && typeof onProgress === 'function') {
      uploadTask.onProgressUpdate((res) => {
        onProgress(res.progress);
      });
    }
  });
};

// 图片压缩
export const compressImage = (filePath, quality = 0.8) => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src: filePath,
      quality: quality,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (err) => {
        console.error('图片压缩失败:', err);
        // 如果压缩失败，返回原图片
        resolve(filePath);
      }
    });
  });
};

// 显示加载提示
export const showLoading = (title = '加载中...') => {
  uni.showLoading({
    title,
    mask: true
  });
};

// 隐藏加载提示
export const hideLoading = () => {
  uni.hideLoading();
};

// 显示成功提示
export const showSuccess = (title = '操作成功') => {
  uni.showToast({
    title,
    icon: 'success',
    duration: 2000
  });
};

// 显示错误提示
export const showError = (title = '操作失败') => {
  uni.showToast({
    title,
    icon: 'none',
    duration: 3000
  });
};

// 显示确认对话框
export const showConfirm = (content, title = '提示') => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm);
      }
    });
  });
};
