const express = require('express');
const { query, validationResult } = require('express-validator');

const { executeQuery } = require('../config/database');
const { createError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取合作学校列表
router.get('/', [
  query('status').optional().isInt({ min: 0, max: 1 }).withMessage('状态必须是0或1')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const { status } = req.query;

  try {
    let whereConditions = ['ss.supplier_id = ?'];
    let queryParams = [req.user.id];

    // 状态筛选
    if (status !== undefined) {
      whereConditions.push('ss.status = ?');
      queryParams.push(status);
    }

    const schoolsQuery = `
      SELECT 
        s.id,
        s.name,
        s.address,
        s.contact_person,
        s.contact_phone,
        s.contact_email,
        ss.contract_number,
        ss.start_date,
        ss.end_date,
        ss.status,
        ss.created_at as cooperation_start,
        ss.updated_at
      FROM supplier_schools ss
      INNER JOIN schools s ON ss.school_id = s.id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ss.created_at DESC
    `;

    const schools = await executeQuery(schoolsQuery, queryParams);

    res.json({
      success: true,
      message: '获取成功',
      data: schools
    });

  } catch (error) {
    throw error;
  }
}));

// 获取学校详情
router.get('/:id', asyncHandler(async (req, res) => {
  const schoolId = req.params.id;

  try {
    const schoolQuery = `
      SELECT 
        s.id,
        s.name,
        s.address,
        s.contact_person,
        s.contact_phone,
        s.contact_email,
        s.description,
        ss.contract_number,
        ss.start_date,
        ss.end_date,
        ss.status,
        ss.created_at as cooperation_start,
        ss.updated_at
      FROM supplier_schools ss
      INNER JOIN schools s ON ss.school_id = s.id
      WHERE s.id = ? AND ss.supplier_id = ?
    `;

    const schools = await executeQuery(schoolQuery, [schoolId, req.user.id]);

    if (schools.length === 0) {
      throw createError('学校不存在或未建立合作关系', 'SCHOOL_NOT_FOUND', 404);
    }

    const school = schools[0];

    // 获取与该学校的订单统计
    const orderStatsQuery = `
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 4 THEN 1 END) as completed_orders,
        COALESCE(SUM(CASE WHEN status = 4 THEN total_amount END), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN status = 4 AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN total_amount END), 0) as month_revenue
      FROM orders 
      WHERE supplier_id = ? AND school_id = ?
    `;

    const orderStats = await executeQuery(orderStatsQuery, [req.user.id, schoolId]);

    // 获取最近的订单
    const recentOrdersQuery = `
      SELECT 
        id,
        order_number,
        total_amount,
        status,
        created_at
      FROM orders 
      WHERE supplier_id = ? AND school_id = ?
      ORDER BY created_at DESC
      LIMIT 5
    `;

    const recentOrders = await executeQuery(recentOrdersQuery, [req.user.id, schoolId]);

    res.json({
      success: true,
      message: '获取成功',
      data: {
        ...school,
        statistics: orderStats[0],
        recent_orders: recentOrders
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 获取学校订单历史
router.get('/:id/orders', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
  query('per_page').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('status').optional().isInt({ min: 1, max: 5 }).withMessage('状态值必须在1-5之间')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const schoolId = req.params.id;
  const {
    page = 1,
    per_page = 10,
    status
  } = req.query;

  try {
    // 验证学校合作关系
    const cooperationCheck = await executeQuery(
      'SELECT id FROM supplier_schools WHERE supplier_id = ? AND school_id = ?',
      [req.user.id, schoolId]
    );

    if (cooperationCheck.length === 0) {
      throw createError('未与该学校建立合作关系', 'NO_COOPERATION', 403);
    }

    let whereConditions = ['supplier_id = ? AND school_id = ?'];
    let queryParams = [req.user.id, schoolId];

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    const baseQuery = `
      SELECT 
        id,
        order_number,
        total_amount,
        subtotal,
        delivery_fee,
        status,
        delivery_address,
        contact_person,
        contact_phone,
        created_at,
        updated_at
      FROM orders
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY created_at DESC
    `;

    const { executePagedQuery } = require('../config/database');
    const result = await executePagedQuery(baseQuery, queryParams, page, per_page);

    res.json({
      success: true,
      message: '获取成功',
      data: {
        items: result.data,
        pagination: result.pagination
      }
    });

  } catch (error) {
    throw error;
  }
}));

module.exports = router;
