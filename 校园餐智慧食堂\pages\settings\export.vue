<template>
  <view class="export-container">
    <!-- 导出类型选择 -->
    <view class="export-section">
      <view class="section-title">
        <text class="title-text">选择导出数据类型</text>
      </view>
      
      <view class="export-types">
        <view 
          v-for="(type, index) in exportTypes" 
          :key="index"
          class="type-item"
          :class="{ 'selected': type.selected }"
          @click="toggleExportType(index)"
        >
          <view class="type-left">
            <text class="type-icon">{{ type.icon }}</text>
            <view class="type-info">
              <text class="type-name">{{ type.name }}</text>
              <text class="type-desc">{{ type.description }}</text>
            </view>
          </view>
          <view class="type-right">
            <text class="check-icon">{{ type.selected ? '✓' : '' }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 时间范围选择 -->
    <view class="export-section">
      <view class="section-title">
        <text class="title-text">选择时间范围</text>
      </view>
      
      <view class="time-range">
        <view class="time-presets">
          <view 
            v-for="(preset, index) in timePresets" 
            :key="index"
            class="preset-item"
            :class="{ 'active': selectedPreset === index }"
            @click="selectTimePreset(index)"
          >
            <text class="preset-text">{{ preset.name }}</text>
          </view>
        </view>
        
        <view class="custom-time">
          <view class="time-item">
            <text class="time-label">开始日期</text>
            <picker 
              mode="date" 
              :value="startDate" 
              @change="handleStartDateChange"
            >
              <view class="time-picker">
                <text class="time-value">{{ startDate || '请选择' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="time-item">
            <text class="time-label">结束日期</text>
            <picker 
              mode="date" 
              :value="endDate" 
              @change="handleEndDateChange"
            >
              <view class="time-picker">
                <text class="time-value">{{ endDate || '请选择' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 导出格式选择 -->
    <view class="export-section">
      <view class="section-title">
        <text class="title-text">选择导出格式</text>
      </view>
      
      <view class="format-options">
        <view 
          v-for="(format, index) in exportFormats" 
          :key="index"
          class="format-item"
          :class="{ 'selected': selectedFormat === index }"
          @click="selectFormat(index)"
        >
          <text class="format-icon">{{ format.icon }}</text>
          <text class="format-name">{{ format.name }}</text>
          <text class="format-desc">{{ format.description }}</text>
        </view>
      </view>
    </view>
    
    <!-- 导出进度 -->
    <view v-if="exporting" class="export-progress">
      <view class="progress-info">
        <text class="progress-text">正在导出数据...</text>
        <text class="progress-percent">{{ exportProgress }}%</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: exportProgress + '%' }"></view>
      </view>
    </view>
    
    <!-- 导出历史 -->
    <view class="export-section">
      <view class="section-title">
        <text class="title-text">导出历史</text>
      </view>
      
      <view v-if="exportHistory.length === 0" class="empty-history">
        <text class="empty-text">暂无导出记录</text>
      </view>
      
      <view v-else class="history-list">
        <view 
          v-for="(record, index) in exportHistory" 
          :key="index"
          class="history-item"
        >
          <view class="history-info">
            <text class="history-name">{{ record.name }}</text>
            <text class="history-time">{{ formatDateTime(record.created_at) }}</text>
          </view>
          <view class="history-actions">
            <button 
              class="action-btn download"
              @click="downloadFile(record)"
              :disabled="!record.download_url"
            >
              下载
            </button>
            <button 
              class="action-btn delete"
              @click="deleteExportRecord(record.id)"
            >
              删除
            </button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 导出按钮 -->
    <view class="export-actions">
      <button 
        class="export-btn"
        @click="startExport"
        :disabled="!canExport || exporting"
      >
        {{ exporting ? '导出中...' : '开始导出' }}
      </button>
    </view>
  </view>
</template>

<script>
import { supplierAPI } from '@/api/supplier.js';
import { formatDateTime } from '@/utils/common.js';
import { showError, showSuccess, showLoading, hideLoading, showConfirm } from '@/utils/request.js';

export default {
  data() {
    return {
      exportTypes: [
        {
          name: '订单数据',
          description: '包含订单详情、状态、金额等信息',
          icon: '📋',
          selected: true,
          type: 'orders'
        },
        {
          name: '产品数据',
          description: '包含产品信息、价格、库存等数据',
          icon: '📦',
          selected: false,
          type: 'products'
        },
        {
          name: '收入数据',
          description: '包含收入统计、趋势分析等数据',
          icon: '💰',
          selected: false,
          type: 'revenue'
        },
        {
          name: '学校数据',
          description: '包含合作学校、合同信息等数据',
          icon: '🏫',
          selected: false,
          type: 'schools'
        }
      ],
      timePresets: [
        { name: '最近7天', days: 7 },
        { name: '最近30天', days: 30 },
        { name: '最近3个月', days: 90 },
        { name: '自定义', days: 0 }
      ],
      selectedPreset: 1,
      startDate: '',
      endDate: '',
      exportFormats: [
        {
          name: 'Excel',
          description: '适合数据分析和编辑',
          icon: '📊',
          format: 'xlsx'
        },
        {
          name: 'CSV',
          description: '通用格式，兼容性好',
          icon: '📄',
          format: 'csv'
        },
        {
          name: 'PDF',
          description: '适合打印和分享',
          icon: '📑',
          format: 'pdf'
        }
      ],
      selectedFormat: 0,
      exporting: false,
      exportProgress: 0,
      exportHistory: []
    };
  },
  
  computed: {
    canExport() {
      const hasSelectedType = this.exportTypes.some(type => type.selected);
      const hasTimeRange = this.startDate && this.endDate;
      return hasSelectedType && hasTimeRange;
    }
  },
  
  onLoad() {
    this.initTimeRange();
    this.loadExportHistory();
  },
  
  methods: {
    // 初始化时间范围
    initTimeRange() {
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      this.endDate = now.toISOString().split('T')[0];
      this.startDate = thirtyDaysAgo.toISOString().split('T')[0];
    },
    
    // 切换导出类型
    toggleExportType(index) {
      this.exportTypes[index].selected = !this.exportTypes[index].selected;
    },
    
    // 选择时间预设
    selectTimePreset(index) {
      this.selectedPreset = index;
      
      if (index < 3) {
        const now = new Date();
        const daysAgo = new Date(now.getTime() - this.timePresets[index].days * 24 * 60 * 60 * 1000);
        
        this.endDate = now.toISOString().split('T')[0];
        this.startDate = daysAgo.toISOString().split('T')[0];
      }
    },
    
    // 开始日期变化
    handleStartDateChange(e) {
      this.startDate = e.detail.value;
      this.selectedPreset = 3; // 切换到自定义
    },
    
    // 结束日期变化
    handleEndDateChange(e) {
      this.endDate = e.detail.value;
      this.selectedPreset = 3; // 切换到自定义
    },
    
    // 选择导出格式
    selectFormat(index) {
      this.selectedFormat = index;
    },
    
    // 开始导出
    async startExport() {
      if (!this.canExport || this.exporting) return;
      
      // 验证时间范围
      if (new Date(this.startDate) > new Date(this.endDate)) {
        showError('开始日期不能晚于结束日期');
        return;
      }
      
      this.exporting = true;
      this.exportProgress = 0;
      
      try {
        // 准备导出参数
        const exportParams = {
          types: this.exportTypes.filter(type => type.selected).map(type => type.type),
          start_date: this.startDate,
          end_date: this.endDate,
          format: this.exportFormats[this.selectedFormat].format
        };
        
        // 模拟导出进度
        const progressInterval = setInterval(() => {
          if (this.exportProgress < 90) {
            this.exportProgress += Math.random() * 10;
          }
        }, 200);
        
        // 调用导出API
        const response = await this.callExportAPI(exportParams);
        
        clearInterval(progressInterval);
        this.exportProgress = 100;
        
        showSuccess('导出完成');
        
        // 刷新导出历史
        this.loadExportHistory();
        
        // 如果有下载链接，提示用户下载
        if (response.download_url) {
          setTimeout(() => {
            uni.showModal({
              title: '导出完成',
              content: '是否立即下载文件？',
              success: (res) => {
                if (res.confirm) {
                  this.downloadFile(response);
                }
              }
            });
          }, 1000);
        }
        
      } catch (error) {
        showError(error.message || '导出失败');
      } finally {
        this.exporting = false;
        setTimeout(() => {
          this.exportProgress = 0;
        }, 2000);
      }
    },
    
    // 调用导出API
    async callExportAPI(params) {
      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            id: Date.now(),
            name: `数据导出_${new Date().toISOString().split('T')[0]}`,
            download_url: 'https://example.com/download/export.xlsx',
            created_at: new Date().toISOString()
          });
        }, 2000);
      });
    },
    
    // 加载导出历史
    async loadExportHistory() {
      try {
        // 从本地存储加载历史记录
        const history = uni.getStorageSync('exportHistory') || [];
        this.exportHistory = history;
      } catch (error) {
        console.error('加载导出历史失败:', error);
      }
    },
    
    // 下载文件
    downloadFile(record) {
      if (!record.download_url) {
        showError('下载链接已失效');
        return;
      }
      
      // 在小程序中，通常需要先下载到临时目录，然后保存到相册或分享
      uni.downloadFile({
        url: record.download_url,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存文件
            uni.saveFile({
              tempFilePath: res.tempFilePath,
              success: (saveRes) => {
                showSuccess('文件已保存');
              },
              fail: (err) => {
                showError('保存失败');
              }
            });
          }
        },
        fail: (err) => {
          showError('下载失败');
        }
      });
    },
    
    // 删除导出记录
    async deleteExportRecord(id) {
      const confirmed = await showConfirm('确认删除此导出记录？');
      if (!confirmed) return;
      
      try {
        // 从历史记录中移除
        this.exportHistory = this.exportHistory.filter(item => item.id !== id);
        
        // 更新本地存储
        uni.setStorageSync('exportHistory', this.exportHistory);
        
        showSuccess('删除成功');
      } catch (error) {
        showError('删除失败');
      }
    },
    
    // 工具方法
    formatDateTime
  }
};
</script>

<style scoped>
.export-container {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.export-section {
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx;
  background: #f8f8f8;
}

.title-text {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.export-types {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.type-item:last-child {
  border-bottom: none;
}

.type-item.selected {
  background: #f0f8ff;
  border-left: 6rpx solid #007AFF;
}

.type-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.type-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.type-info {
  flex: 1;
}

.type-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.type-desc {
  font-size: 24rpx;
  color: #666;
}

.type-right {
  width: 40rpx;
  text-align: center;
}

.check-icon {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
}

.time-range {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.time-presets {
  display: flex;
  margin-bottom: 30rpx;
}

.preset-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  margin-right: 16rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
}

.preset-item:last-child {
  margin-right: 0;
}

.preset-item.active {
  background: #007AFF;
}

.preset-item.active .preset-text {
  color: white;
}

.preset-text {
  font-size: 24rpx;
  color: #666;
}

.custom-time {
  /* 自定义时间选择样式 */
}

.time-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-label {
  font-size: 28rpx;
  color: #333;
  width: 120rpx;
}

.time-picker {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.time-value {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 28rpx;
  color: #999;
}

.format-options {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.format-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.format-item:last-child {
  border-bottom: none;
}

.format-item.selected {
  background: #f0f8ff;
  border-left: 6rpx solid #007AFF;
}

.format-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.format-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}

.format-desc {
  font-size: 24rpx;
  color: #666;
}

.export-progress {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
}

.progress-percent {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: bold;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007AFF;
  transition: width 0.3s ease;
}

.history-list {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  flex: 1;
}

.history-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.history-time {
  font-size: 24rpx;
  color: #666;
}

.history-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  border: none;
}

.action-btn.download {
  background: #007AFF;
  color: white;
}

.action-btn.delete {
  background: #FF3B30;
  color: white;
}

.action-btn:disabled {
  opacity: 0.6;
}

.empty-history {
  text-align: center;
  padding: 60rpx;
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
  font-size: 28rpx;
  color: #666;
}

.export-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.export-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.export-btn:disabled {
  opacity: 0.6;
}
</style>
