const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// JWT认证中间件
const authMiddleware = async (req, res, next) => {
  try {
    // 从请求头获取token
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: '缺少认证令牌',
        code: 'MISSING_TOKEN'
      });
    }

    // 检查token格式
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '认证令牌格式错误',
        code: 'INVALID_TOKEN_FORMAT'
      });
    }

    // 验证token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: '认证令牌已过期',
          code: 'TOKEN_EXPIRED'
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: '认证令牌无效',
          code: 'INVALID_TOKEN'
        });
      } else {
        throw jwtError;
      }
    }

    // 验证用户是否存在且状态正常
    const userQuery = `
      SELECT 
        id,
        username,
        name,
        phone,
        email,
        status,
        created_at,
        updated_at
      FROM suppliers 
      WHERE id = ? AND status = 1
    `;

    const users = await executeQuery(userQuery, [decoded.userId]);

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用',
        code: 'USER_NOT_FOUND'
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: users[0].id,
      username: users[0].username,
      name: users[0].name,
      phone: users[0].phone,
      email: users[0].email,
      status: users[0].status
    };

    // 记录最后活动时间
    try {
      await executeQuery(
        'UPDATE suppliers SET last_login_at = NOW() WHERE id = ?',
        [req.user.id]
      );
    } catch (updateError) {
      // 更新失败不影响主流程，只记录日志
      console.warn('更新用户最后活动时间失败:', updateError.message);
    }

    next();

  } catch (error) {
    console.error('认证中间件错误:', error);
    return res.status(500).json({
      success: false,
      message: '认证服务异常',
      code: 'AUTH_SERVICE_ERROR'
    });
  }
};

// 可选认证中间件（token可有可无）
const optionalAuthMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      req.user = null;
      return next();
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    if (!token) {
      req.user = null;
      return next();
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      const userQuery = `
        SELECT 
          id,
          username,
          name,
          phone,
          email,
          status
        FROM suppliers 
        WHERE id = ? AND status = 1
      `;

      const users = await executeQuery(userQuery, [decoded.userId]);

      if (users.length > 0) {
        req.user = {
          id: users[0].id,
          username: users[0].username,
          name: users[0].name,
          phone: users[0].phone,
          email: users[0].email,
          status: users[0].status
        };
      } else {
        req.user = null;
      }
    } catch (jwtError) {
      req.user = null;
    }

    next();

  } catch (error) {
    console.error('可选认证中间件错误:', error);
    req.user = null;
    next();
  }
};

// 生成JWT token
const generateToken = (userId, expiresIn = process.env.JWT_EXPIRES_IN || '7d') => {
  return jwt.sign(
    { 
      userId,
      type: 'supplier',
      iat: Math.floor(Date.now() / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn }
  );
};

// 验证token（不查询数据库）
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    return null;
  }
};

module.exports = {
  authMiddleware,
  optionalAuthMiddleware,
  generateToken,
  verifyToken
};
