<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image 
          class="avatar" 
          :src="userInfo.avatar || '/static/default-avatar.png'" 
          mode="aspectFill"
          @click="changeAvatar"
        ></image>
        <view class="user-details">
          <text class="username">{{ userInfo.name || '供应商' }}</text>
          <text class="user-phone">{{ userInfo.phone || '未设置手机号' }}</text>
          <view class="user-status">
            <text class="status-text" :class="statusClass">{{ statusText }}</text>
            <text class="rating-text">信用等级：{{ userInfo.rating || 'A' }}</text>
          </view>
        </view>
      </view>
      <text class="edit-icon" @click="editProfile">✏️</text>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-card">
      <view class="stat-item" @click="goToOrders">
        <text class="stat-number">{{ statistics.totalOrders || 0 }}</text>
        <text class="stat-label">总订单</text>
      </view>
      <view class="stat-item" @click="goToProducts">
        <text class="stat-number">{{ statistics.totalProducts || 0 }}</text>
        <text class="stat-label">产品数</text>
      </view>
      <view class="stat-item" @click="goToSchools">
        <text class="stat-number">{{ statistics.cooperatingSchools || 0 }}</text>
        <text class="stat-label">合作学校</text>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="goToSchools">
          <view class="menu-left">
            <text class="menu-icon">🏫</text>
            <text class="menu-text">合作学校</text>
          </view>
          <text class="menu-arrow">></text>
        </view>
        
        <view class="menu-item" @click="goToNotifications">
          <view class="menu-left">
            <text class="menu-icon">🔔</text>
            <text class="menu-text">消息通知</text>
            <text v-if="unreadCount > 0" class="notification-badge">{{ unreadCount > 99 ? '99+' : unreadCount }}</text>
          </view>
          <text class="menu-arrow">></text>
        </view>
        
        <view class="menu-item" @click="goToSettings">
          <view class="menu-left">
            <text class="menu-icon">⚙️</text>
            <text class="menu-text">设置</text>
          </view>
          <text class="menu-arrow">></text>
        </view>
      </view>
      
      <view class="menu-group">
        <view class="menu-item" @click="goToHelp">
          <view class="menu-left">
            <text class="menu-icon">❓</text>
            <text class="menu-text">帮助与反馈</text>
          </view>
          <text class="menu-arrow">></text>
        </view>
        
        <view class="menu-item" @click="goToAbout">
          <view class="menu-left">
            <text class="menu-icon">ℹ️</text>
            <text class="menu-text">关于我们</text>
          </view>
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </view>
    
    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">版本 v1.0.0</text>
    </view>
  </view>
</template>

<script>
import { supplierAPI, notificationAPI, authAPI } from '@/api/supplier.js';
import { showError, showSuccess, showLoading, hideLoading, showConfirm } from '@/utils/request.js';

export default {
  data() {
    return {
      userInfo: {},
      statistics: {},
      unreadCount: 0,
      loading: false
    };
  },
  
  computed: {
    statusClass() {
      return this.userInfo.status === 1 ? 'status-active' : 'status-inactive';
    },
    
    statusText() {
      return this.userInfo.status === 1 ? '合作中' : '已停用';
    }
  },
  
  onLoad() {
    this.loadUserInfo();
    this.loadData();
  },
  
  onShow() {
    this.loadUnreadCount();
  },
  
  onPullDownRefresh() {
    this.loadData().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  
  methods: {
    // 加载用户信息
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.userInfo = userInfo;
      }
    },
    
    // 加载页面数据
    async loadData() {
      this.loading = true;
      showLoading('加载中...');
      
      try {
        // 并行加载数据
        const [profileRes, statisticsRes] = await Promise.all([
          supplierAPI.getProfile(),
          supplierAPI.getStatistics()
        ]);
        
        this.userInfo = { ...this.userInfo, ...profileRes };
        this.statistics = statisticsRes;
        
        // 更新本地存储的用户信息
        uni.setStorageSync('userInfo', this.userInfo);
        
      } catch (error) {
        showError(error.message || '加载失败');
      } finally {
        this.loading = false;
        hideLoading();
      }
    },
    
    // 加载未读消息数量
    async loadUnreadCount() {
      try {
        const result = await notificationAPI.getUnreadCount();
        this.unreadCount = result.count || 0;
      } catch (error) {
        console.error('加载未读消息数量失败:', error);
      }
    },
    
    // 更换头像
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadAvatar(res.tempFilePaths[0]);
        },
        fail: (err) => {
          showError('选择图片失败');
        }
      });
    },
    
    // 上传头像
    async uploadAvatar(filePath) {
      showLoading('上传中...');
      
      try {
        // 这里应该调用上传头像的API
        // const response = await fileAPI.uploadImage(filePath, 'avatar');
        // await supplierAPI.updateProfile({ avatar: response.url });
        
        showSuccess('头像更新成功');
        this.loadData();
      } catch (error) {
        showError(error.message || '上传失败');
      } finally {
        hideLoading();
      }
    },
    
    // 编辑个人资料
    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      });
    },
    
    // 跳转到订单页面
    goToOrders() {
      uni.switchTab({ url: '/pages/orders/index' });
    },
    
    // 跳转到产品页面
    goToProducts() {
      uni.switchTab({ url: '/pages/products/index' });
    },
    
    // 跳转到学校页面
    goToSchools() {
      uni.navigateTo({
        url: '/pages/schools/index'
      });
    },
    
    // 跳转到通知页面
    goToNotifications() {
      uni.navigateTo({
        url: '/pages/notifications/index'
      });
    },
    
    // 跳转到设置页面
    goToSettings() {
      uni.navigateTo({
        url: '/pages/settings/index'
      });
    },
    
    // 跳转到帮助页面
    goToHelp() {
      uni.navigateTo({
        url: '/pages/help/index'
      });
    },
    
    // 跳转到关于页面
    goToAbout() {
      uni.navigateTo({
        url: '/pages/about/index'
      });
    },
    
    // 退出登录
    async handleLogout() {
      const confirmed = await showConfirm('确认退出登录？');
      if (!confirmed) return;
      
      showLoading('退出中...');
      
      try {
        // 调用退出登录API
        await authAPI.logout();
        
        // 清除本地存储
        uni.removeStorageSync('token');
        uni.removeStorageSync('userInfo');
        
        showSuccess('已退出登录');
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/index'
          });
        }, 1000);
        
      } catch (error) {
        // 即使API调用失败，也要清除本地存储
        uni.removeStorageSync('token');
        uni.removeStorageSync('userInfo');
        
        uni.reLaunch({
          url: '/pages/login/index'
        });
      } finally {
        hideLoading();
      }
    }
  }
};
</script>

<style scoped>
.profile-container {
  background: #f8f8f8;
  min-height: 100vh;
}

.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
}

.user-phone {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.status-text {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.status-active {
  background: rgba(76, 175, 80, 0.8);
}

.status-inactive {
  background: rgba(244, 67, 54, 0.8);
}

.rating-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.edit-icon {
  font-size: 40rpx;
  color: white;
  opacity: 0.8;
}

.stats-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.menu-section {
  margin: 20rpx;
}

.menu-group {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
  position: relative;
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -20rpx;
  background: #FF3B30;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999;
}

.logout-section {
  margin: 20rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #FF3B30;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.version-info {
  text-align: center;
  padding: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
}
</style>
