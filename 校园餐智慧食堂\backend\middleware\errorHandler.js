// 全局错误处理中间件
const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // 默认错误响应
  let error = {
    success: false,
    message: '服务器内部错误',
    code: 'INTERNAL_SERVER_ERROR'
  };

  let statusCode = 500;

  // 数据库错误
  if (err.code) {
    switch (err.code) {
      case 'ER_DUP_ENTRY':
        statusCode = 409;
        error.message = '数据已存在，请检查重复字段';
        error.code = 'DUPLICATE_ENTRY';
        break;
      
      case 'ER_NO_REFERENCED_ROW_2':
        statusCode = 400;
        error.message = '关联数据不存在';
        error.code = 'FOREIGN_KEY_CONSTRAINT';
        break;
      
      case 'ER_ROW_IS_REFERENCED_2':
        statusCode = 400;
        error.message = '数据被其他记录引用，无法删除';
        error.code = 'REFERENCED_DATA';
        break;
      
      case 'ER_DATA_TOO_LONG':
        statusCode = 400;
        error.message = '数据长度超出限制';
        error.code = 'DATA_TOO_LONG';
        break;
      
      case 'ER_BAD_NULL_ERROR':
        statusCode = 400;
        error.message = '必填字段不能为空';
        error.code = 'REQUIRED_FIELD_MISSING';
        break;
      
      case 'ECONNREFUSED':
        statusCode = 503;
        error.message = '数据库连接失败';
        error.code = 'DATABASE_CONNECTION_ERROR';
        break;
      
      case 'ETIMEDOUT':
        statusCode = 504;
        error.message = '数据库操作超时';
        error.code = 'DATABASE_TIMEOUT';
        break;
      
      default:
        if (err.sqlMessage) {
          error.message = '数据库操作失败';
          error.code = 'DATABASE_ERROR';
          
          // 在开发环境下显示详细的SQL错误信息
          if (process.env.NODE_ENV === 'development') {
            error.details = err.sqlMessage;
          }
        }
    }
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    error.message = '认证令牌无效';
    error.code = 'INVALID_TOKEN';
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    error.message = '认证令牌已过期';
    error.code = 'TOKEN_EXPIRED';
  }

  // 验证错误
  if (err.name === 'ValidationError') {
    statusCode = 400;
    error.message = '数据验证失败';
    error.code = 'VALIDATION_ERROR';
    
    if (err.errors) {
      error.details = err.errors;
    }
  }

  // 文件上传错误
  if (err.code === 'LIMIT_FILE_SIZE') {
    statusCode = 413;
    error.message = '文件大小超出限制';
    error.code = 'FILE_TOO_LARGE';
  } else if (err.code === 'LIMIT_FILE_COUNT') {
    statusCode = 400;
    error.message = '文件数量超出限制';
    error.code = 'TOO_MANY_FILES';
  } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    statusCode = 400;
    error.message = '不支持的文件字段';
    error.code = 'UNEXPECTED_FILE_FIELD';
  }

  // 自定义业务错误
  if (err.isCustomError) {
    statusCode = err.statusCode || 400;
    error.message = err.message;
    error.code = err.code || 'BUSINESS_ERROR';
    
    if (err.details) {
      error.details = err.details;
    }
  }

  // 网络错误
  if (err.code === 'ENOTFOUND' || err.code === 'ECONNRESET') {
    statusCode = 503;
    error.message = '网络连接失败';
    error.code = 'NETWORK_ERROR';
  }

  // 在开发环境下返回更多错误信息
  if (process.env.NODE_ENV === 'development') {
    error.stack = err.stack;
    error.originalError = {
      name: err.name,
      message: err.message,
      code: err.code
    };
  }

  // 记录错误到日志文件（生产环境）
  if (process.env.NODE_ENV === 'production') {
    // 这里可以集成日志服务，如Winston、Log4js等
    console.error('生产环境错误:', {
      timestamp: new Date().toISOString(),
      error: error,
      request: {
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    });
  }

  res.status(statusCode).json(error);
};

// 创建自定义错误的工具函数
const createError = (message, code = 'CUSTOM_ERROR', statusCode = 400, details = null) => {
  const error = new Error(message);
  error.isCustomError = true;
  error.code = code;
  error.statusCode = statusCode;
  
  if (details) {
    error.details = details;
  }
  
  return error;
};

// 异步错误处理包装器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  createError,
  asyncHandler
};
