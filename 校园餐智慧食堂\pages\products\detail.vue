<template>
  <view class="product-detail-container">
    <view v-if="loading" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>
    
    <view v-else>
      <!-- 产品图片 -->
      <view class="image-section">
        <image 
          class="product-image" 
          :src="productInfo.product_image || '/static/default-product.png'" 
          mode="aspectFill"
          @click="previewImage"
        ></image>
        <view v-if="isEditMode" class="image-upload" @click="uploadImage">
          <text class="upload-icon">📷</text>
          <text class="upload-text">更换图片</text>
        </view>
      </view>
      
      <!-- 产品信息表单 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">产品名称</text>
          <input 
            v-if="isEditMode"
            class="form-input" 
            v-model="productInfo.product_name" 
            placeholder="请输入产品名称"
          />
          <text v-else class="form-value">{{ productInfo.product_name }}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">产品编码</text>
          <input 
            v-if="isEditMode"
            class="form-input" 
            v-model="productInfo.product_code" 
            placeholder="请输入产品编码"
          />
          <text v-else class="form-value">{{ productInfo.product_code }}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">规格说明</text>
          <input 
            v-if="isEditMode"
            class="form-input" 
            v-model="productInfo.specification" 
            placeholder="请输入规格说明"
          />
          <text v-else class="form-value">{{ productInfo.specification }}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">单价（元）</text>
          <input 
            v-if="isEditMode"
            class="form-input" 
            v-model="productInfo.price" 
            type="digit"
            placeholder="请输入单价"
          />
          <text v-else class="form-value">¥{{ formatMoney(productInfo.price) }}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">质量认证</text>
          <input 
            v-if="isEditMode"
            class="form-input" 
            v-model="productInfo.quality_cert" 
            placeholder="请输入质量认证信息"
          />
          <text v-else class="form-value">{{ productInfo.quality_cert || '无' }}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">上架状态</text>
          <view v-if="isEditMode" class="switch-container">
            <switch 
              :checked="productInfo.is_available" 
              @change="handleSwitchChange"
              color="#007AFF"
            />
            <text class="switch-text">{{ productInfo.is_available ? '已上架' : '未上架' }}</text>
          </view>
          <text v-else class="form-value status" :class="getStatusClass()">
            {{ getStatusText() }}
          </text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button 
          v-if="!isEditMode && !isAddMode" 
          class="action-btn secondary"
          @click="enterEditMode"
        >
          编辑产品
        </button>
        <button 
          v-if="isEditMode || isAddMode" 
          class="action-btn primary"
          @click="saveProduct"
          :disabled="!canSave"
        >
          {{ isAddMode ? '添加产品' : '保存修改' }}
        </button>
        <button 
          v-if="isEditMode" 
          class="action-btn outline"
          @click="cancelEdit"
        >
          取消
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { productAPI, fileAPI } from '@/api/supplier.js';
import { formatMoney } from '@/utils/common.js';
import { showError, showSuccess, showLoading, hideLoading, showConfirm } from '@/utils/request.js';

export default {
  data() {
    return {
      productId: '',
      productInfo: {
        product_name: '',
        product_code: '',
        specification: '',
        price: '',
        quality_cert: '',
        is_available: false,
        shelf_status: 0,
        product_image: ''
      },
      originalProductInfo: {},
      loading: false,
      isEditMode: false,
      isAddMode: false
    };
  },
  
  computed: {
    canSave() {
      return this.productInfo.product_name.trim() && 
             this.productInfo.product_code.trim() && 
             this.productInfo.specification.trim() && 
             this.productInfo.price > 0;
    }
  },
  
  onLoad(options) {
    if (options.mode === 'add') {
      this.isAddMode = true;
      this.isEditMode = true;
      uni.setNavigationBarTitle({ title: '添加产品' });
    } else if (options.mode === 'edit') {
      this.isEditMode = true;
      this.productId = options.id;
      uni.setNavigationBarTitle({ title: '编辑产品' });
      this.loadProductDetail();
    } else if (options.id) {
      this.productId = options.id;
      uni.setNavigationBarTitle({ title: '产品详情' });
      this.loadProductDetail();
    } else {
      showError('参数错误');
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  
  methods: {
    // 加载产品详情
    async loadProductDetail() {
      this.loading = true;
      showLoading('加载中...');
      
      try {
        const response = await productAPI.getProductDetail(this.productId);
        this.productInfo = { ...response };
        this.originalProductInfo = { ...response };
        
      } catch (error) {
        showError(error.message || '加载失败');
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } finally {
        this.loading = false;
        hideLoading();
      }
    },
    
    // 进入编辑模式
    enterEditMode() {
      this.isEditMode = true;
      this.originalProductInfo = { ...this.productInfo };
      uni.setNavigationBarTitle({ title: '编辑产品' });
    },
    
    // 取消编辑
    cancelEdit() {
      if (this.isAddMode) {
        uni.navigateBack();
        return;
      }
      
      this.isEditMode = false;
      this.productInfo = { ...this.originalProductInfo };
      uni.setNavigationBarTitle({ title: '产品详情' });
    },
    
    // 保存产品
    async saveProduct() {
      if (!this.canSave) {
        showError('请填写完整的产品信息');
        return;
      }
      
      showLoading(this.isAddMode ? '添加中...' : '保存中...');
      
      try {
        if (this.isAddMode) {
          await productAPI.addProduct(this.productInfo);
          showSuccess('产品添加成功');
          setTimeout(() => {
            uni.navigateBack();
          }, 1000);
        } else {
          await productAPI.updateProduct(this.productId, this.productInfo);
          showSuccess('产品更新成功');
          this.isEditMode = false;
          this.originalProductInfo = { ...this.productInfo };
          uni.setNavigationBarTitle({ title: '产品详情' });
        }
        
      } catch (error) {
        showError(error.message || '操作失败');
      } finally {
        hideLoading();
      }
    },
    
    // 上传图片
    uploadImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.handleImageUpload(res.tempFilePaths[0]);
        },
        fail: (err) => {
          showError('选择图片失败');
        }
      });
    },
    
    // 处理图片上传
    async handleImageUpload(filePath) {
      showLoading('上传中...');
      
      try {
        if (this.isAddMode) {
          // 新增模式，暂存图片路径
          this.productInfo.product_image = filePath;
        } else {
          // 编辑模式，直接上传
          const response = await productAPI.uploadProductImage(this.productId, filePath);
          this.productInfo.product_image = response.url;
        }
        
        showSuccess('图片上传成功');
      } catch (error) {
        showError(error.message || '上传失败');
      } finally {
        hideLoading();
      }
    },
    
    // 预览图片
    previewImage() {
      if (this.productInfo.product_image) {
        uni.previewImage({
          urls: [this.productInfo.product_image]
        });
      }
    },
    
    // 开关状态变化
    handleSwitchChange(e) {
      this.productInfo.is_available = e.detail.value;
    },
    
    // 获取状态样式类
    getStatusClass() {
      if (this.productInfo.shelf_status === 0) return 'status-pending';
      if (this.productInfo.shelf_status === 2) return 'status-rejected';
      return this.productInfo.is_available ? 'status-available' : 'status-unavailable';
    },
    
    // 获取状态文本
    getStatusText() {
      if (this.productInfo.shelf_status === 0) return '待审核';
      if (this.productInfo.shelf_status === 2) return '已拒绝';
      return this.productInfo.is_available ? '已上架' : '未上架';
    },
    
    // 工具方法
    formatMoney
  }
};
</script>

<style scoped>
.product-detail-container {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.loading-state {
  text-align: center;
  padding: 200rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.image-section {
  background: white;
  padding: 40rpx;
  text-align: center;
  position: relative;
}

.product-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
}

.image-upload {
  position: absolute;
  bottom: 60rpx;
  right: 60rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 16rpx 20rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
}

.upload-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.upload-text {
  font-size: 22rpx;
}

.form-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
}

.form-value {
  font-size: 28rpx;
  color: #333;
  line-height: 80rpx;
}

.form-value.status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  color: white;
  display: inline-block;
  line-height: 1;
}

.status-available {
  background: #34C759;
}

.status-unavailable {
  background: #8E8E93;
}

.status-pending {
  background: #FF9500;
}

.status-rejected {
  background: #FF3B30;
}

.switch-container {
  display: flex;
  align-items: center;
}

.switch-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.secondary {
  background: #34C759;
  color: white;
}

.action-btn.outline {
  background: transparent;
  color: #007AFF;
  border: 2rpx solid #007AFF;
}

.action-btn:disabled {
  opacity: 0.6;
}
</style>
