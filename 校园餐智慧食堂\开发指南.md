# 校园餐智慧食堂供应商端小程序开发指南

## 项目概述

本项目是基于uni-app框架开发的微信小程序，为校园餐智慧食堂系统的供应商提供移动端管理功能。项目采用Vue 3 + TypeScript + uni-app的技术栈，支持多端发布。

## 开发环境要求

### 必需软件
- **HBuilderX**: 3.8.0 或更高版本
- **微信开发者工具**: 最新稳定版
- **Node.js**: 16.0 或更高版本

### 推荐软件
- **Git**: 版本控制
- **VS Code**: 代码编辑（可选）

## 项目启动步骤

### 1. 环境准备
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version
```

### 2. 项目配置
1. 打开HBuilderX，导入项目
2. 修改`manifest.json`中的微信小程序AppID
3. 配置后端API地址（在`utils/request.js`中）

### 3. 运行项目
1. 在HBuilderX中点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
2. 首次运行会自动打开微信开发者工具
3. 在微信开发者工具中进行预览和调试

## 项目架构说明

### 目录结构
```
校园餐智慧食堂/
├── api/                    # API接口层
│   └── supplier.js         # 供应商相关接口
├── pages/                  # 页面文件
│   ├── login/              # 登录模块
│   ├── dashboard/          # 首页模块
│   ├── orders/             # 订单模块
│   ├── products/           # 产品模块
│   ├── schools/            # 学校模块
│   └── profile/            # 个人中心模块
├── utils/                  # 工具函数
│   ├── request.js          # 网络请求封装
│   └── common.js           # 通用工具函数
├── static/                 # 静态资源
└── components/             # 自定义组件（待扩展）
```

### 技术架构
- **前端框架**: uni-app (Vue 3)
- **状态管理**: 本地存储 + 组件状态
- **网络请求**: 封装的uni.request
- **UI组件**: 自定义组件
- **样式方案**: SCSS + 原子化CSS类

## 核心功能模块

### 1. 用户认证模块
- **文件位置**: `pages/login/`
- **主要功能**: 
  - 供应商账号密码登录
  - 微信快捷登录
  - 账号绑定
- **关键API**: `/supplier/login`, `/supplier/wx_login`

### 2. 订单管理模块
- **文件位置**: `pages/orders/`
- **主要功能**:
  - 订单列表查看
  - 订单详情查看
  - 订单状态操作
- **关键API**: `/supplier/orders`, `/supplier/orders/{id}`

### 3. 产品管理模块
- **文件位置**: `pages/products/`
- **主要功能**:
  - 产品列表管理
  - 产品信息编辑
  - 产品上架下架
- **关键API**: `/supplier/products`, `/supplier/products/{id}`

### 4. 数据概览模块
- **文件位置**: `pages/dashboard/`
- **主要功能**:
  - 统计数据展示
  - 快捷操作入口
  - 最近订单预览
- **关键API**: `/supplier/statistics`, `/supplier/profile`

## 开发规范

### 代码规范
1. **命名规范**:
   - 文件名: 小写字母 + 连字符 (kebab-case)
   - 变量名: 驼峰命名 (camelCase)
   - 常量名: 大写字母 + 下划线 (UPPER_CASE)

2. **组件规范**:
   - 组件名使用PascalCase
   - Props使用camelCase
   - 事件名使用kebab-case

3. **样式规范**:
   - 使用scoped样式
   - 尺寸单位使用rpx
   - 颜色使用十六进制值

### API调用规范
```javascript
// 推荐的API调用方式
try {
  showLoading('加载中...');
  const response = await orderAPI.getOrders(params);
  // 处理成功响应
} catch (error) {
  showError(error.message || '操作失败');
} finally {
  hideLoading();
}
```

### 错误处理规范
1. 网络请求必须包含错误处理
2. 用户操作提供明确的反馈
3. 关键操作需要二次确认
4. 异常情况提供友好的错误提示

## 调试技巧

### 1. 控制台调试
```javascript
// 在代码中添加调试信息
console.log('调试信息:', data);
console.error('错误信息:', error);
```

### 2. 网络请求调试
- 在微信开发者工具的Network面板查看请求
- 检查请求参数和响应数据
- 验证API接口是否正常

### 3. 真机调试
- 使用微信开发者工具的真机调试功能
- 在手机上测试实际效果
- 检查不同设备的兼容性

## 性能优化

### 1. 图片优化
- 使用适当的图片格式和尺寸
- 实现图片懒加载
- 压缩图片文件大小

### 2. 代码优化
- 避免在循环中进行复杂计算
- 使用防抖和节流优化用户交互
- 合理使用缓存减少重复请求

### 3. 包体积优化
- 移除未使用的代码和资源
- 使用条件编译减少平台特定代码
- 优化静态资源的引用

## 测试策略

### 1. 功能测试
- 测试所有用户操作流程
- 验证数据的正确性
- 检查边界条件处理

### 2. 兼容性测试
- 测试不同版本的微信客户端
- 验证不同设备尺寸的适配
- 检查网络环境的影响

### 3. 性能测试
- 测试页面加载速度
- 检查内存使用情况
- 验证网络请求效率

## 部署流程

### 1. 代码检查
- 运行代码检查工具
- 确保没有语法错误
- 验证功能完整性

### 2. 版本管理
- 更新版本号
- 编写更新日志
- 创建发布标签

### 3. 小程序发布
1. 在微信开发者工具中上传代码
2. 在微信公众平台提交审核
3. 审核通过后发布上线

## 常见问题解决

### Q: 小程序无法正常运行？
A: 检查以下几点：
- AppID配置是否正确
- 网络请求域名是否在白名单中
- 代码是否有语法错误

### Q: API请求失败？
A: 检查：
- 后端服务是否正常运行
- 请求URL是否正确
- 请求参数格式是否正确
- Token是否有效

### Q: 页面样式异常？
A: 检查：
- CSS语法是否正确
- 尺寸单位是否使用rpx
- 是否存在样式冲突

## 扩展开发

### 添加新页面
1. 在`pages`目录下创建新的页面文件夹
2. 在`pages.json`中注册新页面
3. 实现页面逻辑和样式
4. 添加页面间的导航

### 添加新功能
1. 分析功能需求
2. 设计API接口
3. 实现前端页面
4. 进行功能测试

### 集成第三方服务
1. 选择合适的第三方服务
2. 阅读服务文档
3. 配置服务参数
4. 实现集成代码

## 维护指南

### 日常维护
- 定期检查错误日志
- 监控性能指标
- 更新依赖版本
- 备份重要数据

### 问题排查
1. 收集错误信息
2. 复现问题场景
3. 分析问题原因
4. 制定解决方案
5. 验证修复效果

### 版本更新
1. 制定更新计划
2. 开发新功能
3. 进行充分测试
4. 发布新版本
5. 监控更新效果

## 联系方式

如有技术问题或建议，请联系开发团队。

---

*本文档会根据项目发展持续更新，请关注最新版本。*
