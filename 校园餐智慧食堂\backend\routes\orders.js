const express = require('express');
const { query, param, body, validationResult } = require('express-validator');

const { executeQuery, executePagedQuery } = require('../config/database');
const { createError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取订单列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
  query('per_page').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('status').optional().isInt({ min: 1, max: 5 }).withMessage('状态值必须在1-5之间'),
  query('keyword').optional().isLength({ max: 100 }).withMessage('搜索关键词长度不能超过100个字符'),
  query('school_id').optional().isInt({ min: 1 }).withMessage('学校ID必须是大于0的整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const {
    page = 1,
    per_page = 10,
    status,
    keyword,
    school_id
  } = req.query;

  try {
    let whereConditions = ['o.supplier_id = ?'];
    let queryParams = [req.user.id];

    // 状态筛选
    if (status) {
      whereConditions.push('o.status = ?');
      queryParams.push(status);
    }

    // 学校筛选
    if (school_id) {
      whereConditions.push('o.school_id = ?');
      queryParams.push(school_id);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(o.order_number LIKE ? OR s.name LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    const baseQuery = `
      SELECT 
        o.id,
        o.order_number,
        o.school_id,
        s.name as school_name,
        o.total_amount,
        o.subtotal,
        o.delivery_fee,
        o.status,
        o.delivery_address,
        o.contact_person,
        o.contact_phone,
        o.notes,
        o.created_at,
        o.updated_at,
        (SELECT COUNT(*) FROM order_items WHERE order_id = o.id) as total_items,
        (SELECT SUM(quantity) FROM order_items WHERE order_id = o.id) as total_quantity
      FROM orders o
      LEFT JOIN schools s ON o.school_id = s.id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY o.created_at DESC
    `;

    const result = await executePagedQuery(baseQuery, queryParams, page, per_page);

    res.json({
      success: true,
      message: '获取成功',
      data: {
        items: result.data,
        pagination: result.pagination
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 获取订单详情
router.get('/:id', [
  param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const orderId = req.params.id;

  try {
    // 获取订单基本信息
    const orderQuery = `
      SELECT 
        o.id,
        o.order_number,
        o.school_id,
        s.name as school_name,
        s.address as school_address,
        o.total_amount,
        o.subtotal,
        o.delivery_fee,
        o.status,
        o.delivery_address,
        o.contact_person,
        o.contact_phone,
        o.notes,
        o.created_at,
        o.updated_at,
        o.accepted_at,
        o.shipped_at,
        o.completed_at,
        o.cancelled_at,
        o.cancel_reason
      FROM orders o
      LEFT JOIN schools s ON o.school_id = s.id
      WHERE o.id = ? AND o.supplier_id = ?
    `;

    const orders = await executeQuery(orderQuery, [orderId, req.user.id]);

    if (orders.length === 0) {
      throw createError('订单不存在', 'ORDER_NOT_FOUND', 404);
    }

    const order = orders[0];

    // 获取订单商品详情
    const itemsQuery = `
      SELECT 
        oi.id,
        oi.product_id,
        p.product_name,
        p.product_image,
        oi.specification,
        oi.unit_price,
        oi.quantity,
        oi.unit,
        oi.total_price
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = ?
      ORDER BY oi.id
    `;

    const items = await executeQuery(itemsQuery, [orderId]);

    res.json({
      success: true,
      message: '获取成功',
      data: {
        ...order,
        items: items
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 接受订单
router.post('/:id/accept', [
  param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const orderId = req.params.id;

  try {
    // 检查订单状态
    const orderQuery = `
      SELECT id, status, order_number 
      FROM orders 
      WHERE id = ? AND supplier_id = ?
    `;

    const orders = await executeQuery(orderQuery, [orderId, req.user.id]);

    if (orders.length === 0) {
      throw createError('订单不存在', 'ORDER_NOT_FOUND', 404);
    }

    const order = orders[0];

    if (order.status !== 1) {
      throw createError('只能接受待处理状态的订单', 'INVALID_ORDER_STATUS', 400);
    }

    // 更新订单状态
    await executeQuery(
      'UPDATE orders SET status = 2, accepted_at = NOW(), updated_at = NOW() WHERE id = ?',
      [orderId]
    );

    res.json({
      success: true,
      message: '订单已接受',
      data: {
        order_id: orderId,
        order_number: order.order_number,
        status: 2
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 拒绝订单
router.post('/:id/reject', [
  param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数'),
  body('reason').notEmpty().withMessage('拒绝原因不能为空').isLength({ max: 200 }).withMessage('拒绝原因长度不能超过200个字符')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const orderId = req.params.id;
  const { reason } = req.body;

  try {
    // 检查订单状态
    const orderQuery = `
      SELECT id, status, order_number 
      FROM orders 
      WHERE id = ? AND supplier_id = ?
    `;

    const orders = await executeQuery(orderQuery, [orderId, req.user.id]);

    if (orders.length === 0) {
      throw createError('订单不存在', 'ORDER_NOT_FOUND', 404);
    }

    const order = orders[0];

    if (order.status !== 1) {
      throw createError('只能拒绝待处理状态的订单', 'INVALID_ORDER_STATUS', 400);
    }

    // 更新订单状态
    await executeQuery(
      'UPDATE orders SET status = 5, cancelled_at = NOW(), cancel_reason = ?, updated_at = NOW() WHERE id = ?',
      [reason, orderId]
    );

    res.json({
      success: true,
      message: '订单已拒绝',
      data: {
        order_id: orderId,
        order_number: order.order_number,
        status: 5,
        cancel_reason: reason
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 开始配送
router.post('/:id/ship', [
  param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const orderId = req.params.id;

  try {
    // 检查订单状态
    const orderQuery = `
      SELECT id, status, order_number 
      FROM orders 
      WHERE id = ? AND supplier_id = ?
    `;

    const orders = await executeQuery(orderQuery, [orderId, req.user.id]);

    if (orders.length === 0) {
      throw createError('订单不存在', 'ORDER_NOT_FOUND', 404);
    }

    const order = orders[0];

    if (order.status !== 2) {
      throw createError('只能配送已接受状态的订单', 'INVALID_ORDER_STATUS', 400);
    }

    // 更新订单状态
    await executeQuery(
      'UPDATE orders SET status = 3, shipped_at = NOW(), updated_at = NOW() WHERE id = ?',
      [orderId]
    );

    res.json({
      success: true,
      message: '已开始配送',
      data: {
        order_id: orderId,
        order_number: order.order_number,
        status: 3
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 完成订单
router.post('/:id/complete', [
  param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const orderId = req.params.id;

  try {
    // 检查订单状态
    const orderQuery = `
      SELECT id, status, order_number 
      FROM orders 
      WHERE id = ? AND supplier_id = ?
    `;

    const orders = await executeQuery(orderQuery, [orderId, req.user.id]);

    if (orders.length === 0) {
      throw createError('订单不存在', 'ORDER_NOT_FOUND', 404);
    }

    const order = orders[0];

    if (order.status !== 3) {
      throw createError('只能完成配送中状态的订单', 'INVALID_ORDER_STATUS', 400);
    }

    // 更新订单状态
    await executeQuery(
      'UPDATE orders SET status = 4, completed_at = NOW(), updated_at = NOW() WHERE id = ?',
      [orderId]
    );

    res.json({
      success: true,
      message: '订单已完成',
      data: {
        order_id: orderId,
        order_number: order.order_number,
        status: 4
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 获取订单统计
router.get('/statistics/summary', asyncHandler(async (req, res) => {
  try {
    const supplierId = req.user.id;

    const statsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 1) as pending,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 2) as accepted,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 3) as shipping,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 4) as completed,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 5) as cancelled
    `;

    const stats = await executeQuery(statsQuery, [
      supplierId, supplierId, supplierId, supplierId, supplierId
    ]);

    res.json({
      success: true,
      message: '获取成功',
      data: stats[0]
    });

  } catch (error) {
    throw error;
  }
}));

module.exports = router;
