好的，以下是**供应商、用户、食材、财务**模块及其主要关联表的**详细表名、字段、类型、主外键、说明**，便于你针对性开发微信小程序。内容基于模型定义和迁移脚本，字段类型和约束均已详细列出。

---

## 1. 供应商模块

### 1.1 供应商表 `suppliers`
| 字段名            | 类型                | 主键/外键         | 说明           |
|-------------------|---------------------|-------------------|----------------|
| id                | Integer             | PK                | 主键           |
| name              | String(100)         |                   | 供应商名称     |
| contact_person    | String(50)          |                   | 联系人         |
| phone             | String(20)          |                   | 联系电话       |
| email             | String(100)         |                   | 电子邮箱       |
| address           | String(200)         |                   | 地址           |
| business_license  | String(200)         |                   | 营业执照       |
| tax_id            | String(50)          |                   | 税号           |
| bank_name         | String(100)         |                   | 开户银行       |
| bank_account      | String(50)          |                   | 银行账号       |
| status            | Integer             |                   | 0-停用, 1-合作中|
| rating            | Float               |                   | 信用等级       |
| category_id       | Integer             | FK(supplier_categories.id) | 供应商分类   |
| created_at        | DATETIME2(1)        |                   | 创建时间       |
| updated_at        | DATETIME2(1)        |                   | 更新时间       |

---

### 1.2 供应商分类表 `supplier_categories`
| 字段名      | 类型         | 主键/外键 | 说明     |
|-------------|--------------|-----------|----------|
| id          | Integer      | PK        | 主键     |
| name        | String(50)   |           | 分类名称 |
| description | String(500)  |           | 说明     |
| created_at  | DATETIME2(1) |           | 创建时间 |

---

### 1.3 供应商证书表 `supplier_certificates`
| 字段名            | 类型         | 主键/外键         | 说明           |
|-------------------|--------------|-------------------|----------------|
| id                | Integer      | PK                | 主键           |
| supplier_id       | Integer      | FK(suppliers.id)  | 供应商ID       |
| certificate_type  | String(50)   |                   | 证书类型       |
| certificate_number| String(100)  |                   | 证书编号       |
| issue_date        | Date         |                   | 发证日期       |
| expiry_date       | Date         |                   | 到期日期       |
| issuing_authority | String(100)  |                   | 发证机关       |
| certificate_image | String(200)  |                   | 证书图片       |
| status            | String(20)   |                   | 状态           |
| created_at        | DATETIME2(1) |                   | 创建时间       |
| updated_at        | DATETIME2(1) |                   | 更新时间       |

---

### 1.4 供应商-学校关联表 `supplier_school_relations`
| 字段名         | 类型         | 主键/外键         | 说明           |
|----------------|--------------|-------------------|----------------|
| id             | Integer      | PK                | 主键           |
| supplier_id    | Integer      | FK(suppliers.id)  | 供应商ID       |
| area_id        | Integer      | FK(administrative_areas.id) | 学校区域ID |
| contract_number| String(100)  |                   | 合同编号       |
| start_date     | Date         |                   | 合作开始日期   |
| end_date       | Date         |                   | 合作结束日期   |
| status         | Integer      |                   | 0-已终止, 1-有效|
| notes          | Text         |                   | 备注           |
| created_at     | DATETIME2(1) |                   | 创建时间       |
| updated_at     | DATETIME2(1) |                   | 更新时间       |

---

### 1.5 供应商品表 `supplier_products`
| 字段名            | 类型         | 主键/外键         | 说明           |
|-------------------|--------------|-------------------|----------------|
| id                | Integer      | PK                | 主键           |
| supplier_id       | Integer      | FK(suppliers.id)  | 供应商ID       |
| ingredient_id     | Integer      | FK(ingredients.id)| 食材ID         |
| product_code      | String(50)   |                   | 产品编码       |
| product_name      | String(100)  |                   | 产品名称       |
| model_number      | String(50)   |                   | 型号           |
| specification     | String(100)  |                   | 规格           |
| price             | Numeric(10,2)|                   | 单价           |
| quality_cert      | String(200)  |                   | 质量认证       |
| quality_standard  | String(200)  |                   | 质量标准       |
| product_image     | String(200)  |                   | 产品图片       |
| lead_time         | Integer      |                   | 供货周期(天)   |
| min_order_quantity| Float        |                   | 最小订购量     |
| is_available      | Integer      |                   | 0-未上架, 1-已上架|
| shelf_status      | Integer      |                   | 0-待审核, 1-已审核, 2-已拒绝|
| shelf_time        | DATETIME2(1) |                   | 上架时间       |
| shelf_operator_id | Integer      | FK(users.id)      | 上架操作人     |
| description       | Text         |                   | 产品描述       |
| batch_id          | Integer      | FK(product_batches.id) | 批次ID    |
| created_at        | DATETIME2(1) |                   | 创建时间       |
| updated_at        | DATETIME2(1) |                   | 更新时间       |

---

## 2. 用户模块

### 2.1 用户表 `users`
| 字段名      | 类型         | 主键/外键         | 说明           |
|-------------|--------------|-------------------|----------------|
| id          | Integer      | PK                | 主键           |
| username    | String(80)   | unique            | 用户名         |
| password_hash| String(128) |                   | 密码Hash       |
| email       | String(100)  | unique            | 邮箱           |
| real_name   | String(50)   |                   | 真实姓名       |
| phone       | String(20)   |                   | 手机号         |
| avatar      | String(200)  |                   | 头像           |
| last_login  | DATETIME2(1) |                   | 最后登录时间   |
| status      | Integer      |                   | 0-禁用, 1-启用 |
| area_id     | Integer      | FK(administrative_areas.id) | 区域ID |
| area_level  | Integer      |                   | 区域级别       |
| created_at  | DATETIME2(1) |                   | 创建时间       |

---

### 2.2 角色表 `roles`
| 字段名      | 类型         | 主键/外键 | 说明     |
|-------------|--------------|-----------|----------|
| id          | Integer      | PK        | 主键     |
| name        | String(50)   | unique    | 角色名   |
| description | String(200)  |           | 说明     |
| permissions | Text         |           | 权限JSON |
| created_at  | DATETIME2(1) |           | 创建时间 |

---

### 2.3 用户角色关联表 `user_roles`
| 字段名      | 类型    | 主键/外键         | 说明     |
|-------------|---------|-------------------|----------|
| user_id     | Integer | PK, FK(users.id)  | 用户ID   |
| role_id     | Integer | PK, FK(roles.id)  | 角色ID   |

---

## 3. 食材模块

### 3.1 食材表 `ingredients`
| 字段名            | 类型         | 主键/外键         | 说明           |
|-------------------|--------------|-------------------|----------------|
| id                | Integer      | PK                | 主键           |
| name              | String(100)  |                   | 食材名称       |
| category          | String(50)   |                   | 分类           |
| category_id       | Integer      | FK(ingredient_categories.id) | 分类ID |
| area_id           | Integer      | FK(administrative_areas.id)  | 区域ID |
| unit              | String(20)   |                   | 单位           |
| standard_unit     | String(20)   |                   | 标准单位       |
| base_image        | String(200)  |                   | 图片           |
| storage_temp      | String(50)   |                   | 存储温度       |
| storage_condition | String(100)  |                   | 存储条件       |
| shelf_life        | Integer      |                   | 保质期(天)     |
| specification     | String(100)  |                   | 规格           |
| nutrition_info    | Text         |                   | 营养信息(JSON) |
| is_condiment      | Boolean      |                   | 是否调味品     |
| is_global         | Boolean      |                   | 是否全局食材   |
| status            | Integer      |                   | 0-停用, 1-启用 |
| created_at        | DATETIME2(1) |                   | 创建时间       |
| updated_at        | DATETIME2(1) |                   | 更新时间       |

---

### 3.2 食材分类表 `ingredient_categories`
| 字段名      | 类型         | 主键/外键         | 说明     |
|-------------|--------------|-------------------|----------|
| id          | Integer      | PK                | 主键     |
| name        | String(50)   |                   | 分类名   |
| parent_id   | Integer      | FK(ingredient_categories.id) | 父分类ID |
| description | String(200)  |                   | 说明     |
| created_at  | DATETIME2(1) |                   | 创建时间 |
| updated_at  | DATETIME2(1) |                   | 更新时间|

---

### 3.3 食材批次表 `material_batches`
| 字段名            | 类型         | 主键/外键         | 说明           |
|-------------------|--------------|-------------------|----------------|
| id                | Integer      | PK                | 主键           |
| batch_number      | String(50)   | unique            | 批次编号       |
| ingredient_id     | Integer      | FK(ingredients.id)| 食材ID         |
| supplier_id       | Integer      | FK(suppliers.id)  | 供应商ID       |
| production_date   | Date         |                   | 生产日期       |
| expiry_date       | Date         |                   | 过期日期       |
| production_batch_no| String(50)  |                   | 生产批号       |
| origin_place      | String(100)  |                   | 产地           |
| inspection_no     | String(50)   |                   | 检验编号       |
| certificate_no    | String(50)   |                   | 合格证编号     |
| initial_quantity  | Float        |                   | 初始数量       |
| current_quantity  | Float        |                   | 当前库存       |
| unit              | String(20)   |                   | 单位           |
| unit_price        | Numeric(10,2)|                   | 单价           |
| status            | String(20)   |                   | 状态           |
| area_id           | Integer      | FK(administrative_areas.id) | 区域ID |
| remark            | Text         |                   | 备注           |
| created_at        | DATETIME2(1) |                   | 创建时间       |
| updated_at        | DATETIME2(1) |                   | 更新时间       |

---

## 4. 财务模块

### 4.1 会计科目表 `accounting_subjects`
| 字段名         | 类型         | 主键/外键         | 说明           |
|----------------|--------------|-------------------|----------------|
| id             | Integer      | PK                | 主键           |
| code           | String(20)   |                   | 科目编码       |
| name           | String(100)  |                   | 科目名称       |
| parent_id      | Integer      | FK(accounting_subjects.id) | 父科目ID |
| level          | Integer      |                   | 科目级别       |
| subject_type   | String(20)   |                   | 资产/负债等    |
| balance_direction| String(10) |                   | 借方/贷方      |
| area_id        | Integer      | FK(administrative_areas.id) | 区域ID |
| is_system      | Boolean      |                   | 是否系统预设   |
| is_active      | Boolean      |                   | 是否启用       |
| description    | String(500)  |                   | 说明           |
| created_by     | Integer      | FK(users.id)      | 创建人         |
| created_at     | DATETIME2(1) |                   | 创建时间       |
| updated_at     | DATETIME2(1) |                   | 更新时间       |

---

### 4.2 财务凭证表 `financial_vouchers`
| 字段名         | 类型         | 主键/外键         | 说明           |
|----------------|--------------|-------------------|----------------|
| id             | Integer      | PK                | 主键           |
| voucher_number | String(20)   |                   | 凭证号         |
| voucher_date   | Date         |                   | 凭证日期       |
| area_id        | Integer      | FK(administrative_areas.id) | 区域ID |
| voucher_type   | String(50)   |                   | 凭证类型       |
| summary        | String(200)  |                   | 摘要           |
| total_amount   | Numeric(12,2)|                   | 总金额         |
| status         | String(20)   |                   | 状态           |
| source_type    | String(50)   |                   | 来源类型       |
| source_id      | Integer      |                   | 来源ID         |
| attachment_count| Integer     |                   | 附件数         |
| created_by     | Integer      | FK(users.id)      | 创建人         |
| reviewed_by    | Integer      | FK(users.id)      | 审核人         |
| reviewed_at    | DATETIME2(1) |                   | 审核时间       |
| posted_by      | Integer      | FK(users.id)      | 记账人         |
| posted_at      | DATETIME2(1) |                   | 记账时间       |
| notes          | Text         |                   | 备注           |
| created_at     | DATETIME2(1) |                   | 创建时间       |
| updated_at     | DATETIME2(1) |                   | 更新时间       |

---

### 4.3 凭证明细表 `voucher_details`
| 字段名         | 类型         | 主键/外键         | 说明           |
|----------------|--------------|-------------------|----------------|
| id             | Integer      | PK                | 主键           |
| voucher_id     | Integer      | FK(financial_vouchers.id) | 凭证ID |
| line_number    | Integer      |                   | 行号           |
| subject_id     | Integer      | FK(accounting_subjects.id) | 科目ID |
| summary        | String(200)  |                   | 摘要           |
| debit_amount   | Numeric(12,2)|                   | 借方金额       |
| credit_amount  | Numeric(12,2)|                   | 贷方金额       |
| auxiliary_info | String(200)  |                   | 辅助信息       |
| created_at     | DATETIME2(1) |                   | 创建时间       |

---

### 4.4 应付账款表 `account_payables`
| 字段名         | 类型         | 主键/外键         | 说明           |
|----------------|--------------|-------------------|----------------|
| id             | Integer      | PK                | 主键           |
| payable_number | String(50)   |                   | 应付账款编号   |
| area_id        | Integer      | FK(administrative_areas.id) | 区域ID |
| supplier_id    | Integer      | FK(suppliers.id)  | 供应商ID       |
| stock_in_id    | Integer      | FK(stock_ins.id)  | 入库单ID       |
| purchase_order_id| Integer    | FK(purchase_orders.id) | 采购订单ID |
| original_amount| Numeric(12,2)|                   | 原始金额       |
| paid_amount    | Numeric(12,2)|                   | 已付金额       |
| balance_amount | Numeric(12,2)|                   | 余额           |
| due_date       | Date         |                   | 到期日         |
| status         | String(20)   |                   | 状态           |
| payment_terms  | String(200)  |                   | 付款条件       |
| invoice_number | String(50)   |                   | 发票号         |
| invoice_date   | Date         |                   | 发票日期       |
| invoice_amount | Numeric(12,2)|                   | 发票金额       |
| created_by     | Integer      | FK(users.id)      | 创建人         |
| notes          | Text         |                   | 备注           |
| created_at     | DATETIME2(1) |                   | 创建时间       |
| updated_at     | DATETIME2(1) |                   | 更新时间       |

---

### 4.5 付款记录表 `payment_records`
| 字段名         | 类型         | 主键/外键         | 说明           |
|----------------|--------------|-------------------|----------------|
| id             | Integer      | PK                | 主键           |
| payment_number | String(50)   |                   | 付款编号       |
| area_id        | Integer      | FK(administrative_areas.id) | 区域ID |
| payment_date   | Date         |                   | 付款日期       |
| amount         | Numeric(12,2)|                   | 金额           |
| payment_method | String(30)   |                   | 付款方式       |
| payable_id     | Integer      | FK(account_payables.id) | 应付账款ID |
| supplier_id    | Integer      | FK(suppliers.id)  | 供应商ID       |
| bank_account   | String(50)   |                   | 银行账号       |
| reference_number| String(50)  |                   | 参考号         |
| voucher_id     | Integer      | FK(financial_vouchers.id) | 凭证ID |
| summary        | String(200)  |                   | 摘要           |
| status         | String(20)   |                   | 状态           |
| created_by     | Integer      | FK(users.id)      | 创建人         |
| reviewed_by    | Integer      | FK(users.id)      | 审核人         |
| reviewed_at    | DATETIME2(1) |                   | 审核时间       |
| notes          | Text         |                   | 备注           |
| created_at     | DATETIME2(1) |                   | 创建时间       |
| updated_at     | DATETIME2(1) |                   | 更新时间       |

---

## 5. 主要关联表（部分）

### 5.1 采购订单项表 `purchase_order_items`
| 字段名         | 类型         | 主键/外键         | 说明           |
|----------------|--------------|-------------------|----------------|
| id             | Integer      | PK                | 主键           |
| order_id       | Integer      | FK(purchase_orders.id) | 采购订单ID |
| product_id     | Integer      | FK(supplier_products.id) | 供应商品ID |
| ingredient_id  | Integer      | FK(ingredients.id)| 食材ID         |
| quantity       | Float        |                   | 数量           |
| unit           | String(20)   |                   | 单位           |
| unit_price     | Numeric(10,2)|                   | 单价           |
| total_price    | Numeric(10,2)|                   | 总价           |
| received_quantity| Float      |                   | 已收数量       |
| notes          | Text         |                   | 备注           |
| created_at     | DATETIME2(1) |                   | 创建时间       |
| updated_at     | DATETIME2(1) |                   | 更新时间       |

---

如需**更多表（如入库、出库、批次、库存等）**的详细结构，或需要**ER图/表关系图**，请随时告知！  
如需**微信小程序接口字段建议**，也可进一步说明业务场景。
我将为您提供更多表的详细结构，并解释它们之间的关系。让我们先查看相关的表结构。


Search files...
让我为您详细说明库存、入库、出库和批次相关的表结构及其关系：

1. **库存表 (Inventory)**
```sql
CREATE TABLE inventories (
    id INT PRIMARY KEY,
    warehouse_id INT NOT NULL,  -- 关联仓库
    storage_location_id INT NOT NULL,  -- 关联存储位置
    ingredient_id INT NOT NULL,  -- 关联食材
    batch_number VARCHAR(50) NOT NULL,  -- 批次号
    quantity FLOAT NOT NULL,  -- 数量
    unit VARCHAR(20) NOT NULL,  -- 单位
    production_date DATE NOT NULL,  -- 生产日期
    expiry_date DATE NOT NULL,  -- 过期日期
    supplier_id INT,  -- 关联供应商
    status VARCHAR(20) NOT NULL DEFAULT '正常',  -- 状态：正常/待检/冻结/已过期
    notes TEXT,  -- 备注
    created_at DATETIME2(1) NOT NULL,  -- 创建时间
    updated_at DATETIME2(1) NOT NULL  -- 更新时间
)
```

2. **入库记录表 (StockIn)**
```sql
CREATE TABLE stock_ins (
    id INT PRIMARY KEY,
    stock_in_number VARCHAR(50) NOT NULL UNIQUE,  -- 入库单号
    warehouse_id INT NOT NULL,  -- 关联仓库
    delivery_id INT,  -- 关联送货单
    purchase_order_id INT,  -- 关联采购订单
    stock_in_date DATE NOT NULL,  -- 入库日期
    stock_in_type VARCHAR(20) NOT NULL,  -- 入库类型：采购入库/调拨入库/退货入库
    operator_id INT NOT NULL,  -- 操作员
    inspector_id INT,  -- 检验员
    status VARCHAR(20) NOT NULL DEFAULT '待审核',  -- 状态：待审核/已审核/已入库/已取消
    notes TEXT,  -- 备注
    is_financial_confirmed BOOLEAN DEFAULT FALSE,  -- 是否财务确认
    total_cost DECIMAL(15,2),  -- 总成本
    supplier_id INT,  -- 供应商ID
    payable_id INT,  -- 应付账款ID
    voucher_id INT,  -- 财务凭证ID
    area_id INT,  -- 学校区域ID
    created_at DATETIME2(1) NOT NULL,
    updated_at DATETIME2(1) NOT NULL
)
```

3. **入库明细表 (StockInItem)**
```sql
CREATE TABLE stock_in_items (
    id INT PRIMARY KEY,
    stock_in_id INT NOT NULL,  -- 关联入库单
    ingredient_id INT NOT NULL,  -- 关联食材
    batch_number VARCHAR(50) NOT NULL,  -- 批次号
    quantity FLOAT NOT NULL,  -- 数量
    unit VARCHAR(20) NOT NULL,  -- 单位
    unit_price FLOAT,  -- 单价
    production_date DATE NOT NULL,  -- 生产日期
    expiry_date DATE NOT NULL,  -- 过期日期
    storage_location_id INT NOT NULL,  -- 存储位置
    supplier_id INT,  -- 供应商
    purchase_order_item_id INT,  -- 关联采购订单明细
    quality_check_result VARCHAR(20),  -- 质检结果：合格/不合格
    quality_check_notes TEXT,  -- 质检备注
    quality_status VARCHAR(20) DEFAULT '良好',  -- 质量状态：良好/一般/较差
    notes TEXT,  -- 备注
    created_at DATETIME2(1) NOT NULL,
    updated_at DATETIME2(1) NOT NULL
)
```

4. **出库记录表 (StockOut)**
```sql
CREATE TABLE stock_outs (
    id INT PRIMARY KEY,
    stock_out_number VARCHAR(50) NOT NULL UNIQUE,  -- 出库单号
    warehouse_id INT NOT NULL,  -- 关联仓库
    consumption_plan_id INT,  -- 关联消耗计划
    stock_out_date DATE NOT NULL,  -- 出库日期
    stock_out_type VARCHAR(20) NOT NULL,  -- 出库类型：消耗出库/调拨出库/报废出库
    recipient VARCHAR(50),  -- 领用人
    department VARCHAR(50),  -- 领用部门
    operator_id INT NOT NULL,  -- 操作员
    approver_id INT,  -- 审批人
    status VARCHAR(20) NOT NULL DEFAULT '待审核',  -- 状态：待审核/已审核/已出库/已取消
    notes TEXT,  -- 备注
    created_at DATETIME2(1) NOT NULL,
    updated_at DATETIME2(1) NOT NULL
)
```

5. **出库明细表 (StockOutItem)**
```sql
CREATE TABLE stock_out_items (
    id INT PRIMARY KEY,
    stock_out_id INT NOT NULL,  -- 关联出库单
    inventory_id INT NOT NULL,  -- 关联库存
    ingredient_id INT NOT NULL,  -- 关联食材
    batch_number VARCHAR(50) NOT NULL,  -- 批次号
    quantity FLOAT NOT NULL,  -- 数量
    unit VARCHAR(20) NOT NULL,  -- 单位
    consumption_detail_id INT,  -- 关联消耗明细
    notes TEXT,  -- 备注
    created_at DATETIME2(1) NOT NULL,
    updated_at DATETIME2(1) NOT NULL
)
```

**表关系说明：**

1. **入库流程**：
   - `StockIn` (入库单) -> `StockInItem` (入库明细) -> `Inventory` (库存)
   - 一个入库单可以有多个入库明细
   - 每个入库明细对应一个批次的库存记录

2. **出库流程**：
   - `StockOut` (出库单) -> `StockOutItem` (出库明细) -> `Inventory` (库存)
   - 一个出库单可以有多个出库明细
   - 每个出库明细必须关联到一个具体的库存记录

3. **批次管理**：
   - 通过 `batch_number` 字段追踪每个批次的食材
   - 批次信息包含：生产日期、过期日期、供应商、质检结果等
   - 支持批次追溯：可以从出库记录追溯到入库记录

4. **库存管理**：
   - 库存记录按仓库、存储位置、食材、批次进行管理
   - 支持库存状态管理：正常/待检/冻结/已过期
   - 支持库存数量实时更新

5. **特殊功能**：
   - 支持平账入库：当库存不足时自动生成平账入库记录
   - 支持质量追溯：可以追踪每个批次的质检结果
   - 支持财务关联：入库单可以关联应付账款和财务凭证

这个数据库设计支持完整的库存管理流程，包括入库、出库、批次管理和库存追踪。每个操作都有详细的记录，支持追溯和审计。
让我检查一下项目中实际存在的表。


Search files...
根据代码分析，项目中已有的相关表结构如下：

1. **入库记录表 (stock_ins)**
```sql
CREATE TABLE stock_ins (
    id INT PRIMARY KEY,
    stock_in_number VARCHAR(50) NOT NULL UNIQUE,  -- 入库单号
    warehouse_id INT NOT NULL,  -- 关联仓库
    delivery_id INT,  -- 关联送货单
    purchase_order_id INT,  -- 关联采购订单
    stock_in_date DATE NOT NULL,  -- 入库日期
    stock_in_type VARCHAR(20) NOT NULL,  -- 入库类型：采购入库/调拨入库/退货入库
    operator_id INT NOT NULL,  -- 操作员
    inspector_id INT,  -- 检验员
    status VARCHAR(20) NOT NULL DEFAULT '待审核',  -- 状态：待审核/已审核/已入库/已取消
    notes TEXT,  -- 备注
    is_financial_confirmed BOOLEAN DEFAULT FALSE,  -- 是否财务确认
    total_cost DECIMAL(15,2),  -- 总成本
    supplier_id INT,  -- 供应商ID
    payable_id INT,  -- 应付账款ID
    voucher_id INT,  -- 财务凭证ID
    area_id INT,  -- 学校区域ID
    created_at DATETIME2(1) NOT NULL,
    updated_at DATETIME2(1) NOT NULL
)
```

2. **入库明细表 (stock_in_items)**
```sql
CREATE TABLE stock_in_items (
    id INT PRIMARY KEY,
    stock_in_id INT NOT NULL,  -- 关联入库单
    ingredient_id INT NOT NULL,  -- 关联食材
    batch_number VARCHAR(50) NOT NULL,  -- 批次号
    quantity FLOAT NOT NULL,  -- 数量
    unit VARCHAR(20) NOT NULL,  -- 单位
    unit_price FLOAT,  -- 单价
    production_date DATE NOT NULL,  -- 生产日期
    expiry_date DATE NOT NULL,  -- 过期日期
    storage_location_id INT NOT NULL,  -- 存储位置
    supplier_id INT,  -- 供应商
    purchase_order_item_id INT,  -- 关联采购订单明细
    quality_check_result VARCHAR(20),  -- 质检结果：合格/不合格
    quality_check_notes TEXT,  -- 质检备注
    quality_status VARCHAR(20) DEFAULT '良好',  -- 质量状态：良好/一般/较差
    notes TEXT,  -- 备注
    created_at DATETIME2(1) NOT NULL,
    updated_at DATETIME2(1) NOT NULL
)
```

3. **入库单据表 (stock_in_documents)**
```sql
CREATE TABLE stock_in_documents (
    id INT PRIMARY KEY,
    stock_in_id INT NOT NULL,  -- 关联入库单
    document_type VARCHAR(50) NOT NULL,  -- 单据类型：送货单/检验检疫证明/质量检测报告等
    file_path VARCHAR(255) NOT NULL,  -- 文件路径
    supplier_id INT,  -- 关联供应商
    notes TEXT,  -- 备注
    created_at DATETIME2(1) NOT NULL
)
```

4. **食材检验检疫记录表 (ingredient_inspections)**
```sql
CREATE TABLE ingredient_inspections (
    id INT PRIMARY KEY,
    stock_in_item_id INT NOT NULL,  -- 关联入库明细
    inspector_id INT NOT NULL,  -- 检验员
    inspection_date DATE NOT NULL,  -- 检验日期
    inspection_type VARCHAR(50) NOT NULL,  -- 检验类型：感官检验/理化检验/微生物检验等
    result VARCHAR(20) NOT NULL,  -- 合格/不合格
    notes TEXT,  -- 备注
    document_id INT,  -- 关联单据
    created_at DATETIME2(1) NOT NULL
)
```

5. **食材批次表 (material_batches)**
```sql
CREATE TABLE material_batches (
    id INT PRIMARY KEY,
    batch_number VARCHAR(50) NOT NULL,  -- 批次号
    ingredient_id INT NOT NULL,  -- 关联食材
    supplier_id INT NOT NULL,  -- 关联供应商
    production_date DATE NOT NULL,  -- 生产日期
    expiry_date DATE NOT NULL,  -- 过期日期
    production_batch_no VARCHAR(50),  -- 生产批次号
    origin_place VARCHAR(100),  -- 产地
    inspection_no VARCHAR(50),  -- 检验检疫编号
    certificate_no VARCHAR(50),  -- 证书编号
    initial_quantity FLOAT NOT NULL,  -- 初始数量
    current_quantity FLOAT NOT NULL,  -- 当前数量
    unit VARCHAR(20) NOT NULL,  -- 单位
    unit_price DECIMAL(10,2),  -- 单价
    status VARCHAR(20) NOT NULL,  -- 状态
    area_id INT NOT NULL,  -- 学校区域ID
    remark TEXT,  -- 备注
    created_at DATETIME2(1) NOT NULL,
    updated_at DATETIME2(1) NOT NULL
)
```

这些表已经完整覆盖了入库流程所需的功能：
1. 入库单据管理（stock_in_documents）
2. 质检管理（ingredient_inspections）
3. 批次管理（material_batches）
4. 财务关联（在 stock_ins 表中）
5. 文档管理（stock_in_documents）

每个表都有其特定的用途，并且表之间通过外键关联，形成了完整的数据关系网络。
