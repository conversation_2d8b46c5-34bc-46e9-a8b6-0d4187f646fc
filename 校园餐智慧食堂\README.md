# 校园餐智慧食堂 - 供应商端小程序

## 项目简介

这是一个基于uni-app开发的微信小程序，为校园餐智慧食堂系统的供应商提供移动端管理功能。供应商可以通过小程序管理订单、产品、查看合作学校信息等。

## 功能特性

### 🔐 用户认证
- 供应商账号密码登录
- 微信快捷登录
- 微信账号绑定供应商账号
- 记住密码功能

### 📊 数据概览
- 供应商中心首页
- 订单统计数据
- 收入趋势分析
- 快捷操作入口

### 📋 订单管理
- 订单列表查看（支持状态筛选）
- 订单详情查看
- 订单状态操作（接受/拒绝/配送/完成）
- 订单搜索功能

### 📦 产品管理
- 产品列表管理
- 产品上架/下架
- 产品信息编辑
- 产品图片上传

### 🏫 合作学校
- 合作学校列表
- 合同信息查看
- 学校联系方式
- 学校订单历史

### 👤 个人中心
- 个人信息管理
- 头像更换
- 消息通知
- 设置选项

## 技术栈

- **框架**: uni-app (Vue 3)
- **开发工具**: HBuilderX
- **UI组件**: 自定义组件
- **状态管理**: Vuex (可选)
- **网络请求**: uni.request 封装
- **平台**: 微信小程序

## 项目结构

```
校园餐智慧食堂/
├── pages/                  # 页面文件
│   ├── login/              # 登录页面
│   ├── dashboard/          # 供应商中心首页
│   ├── orders/             # 订单管理
│   ├── products/           # 产品管理
│   ├── schools/            # 合作学校
│   └── profile/            # 个人中心
├── api/                    # API接口
│   └── supplier.js         # 供应商相关API
├── utils/                  # 工具函数
│   ├── request.js          # 网络请求封装
│   └── common.js           # 通用工具函数
├── static/                 # 静态资源
│   ├── images/             # 图片资源
│   └── logo.png            # 应用图标
├── App.uvue                # 应用配置
├── main.uts                # 入口文件
├── pages.json              # 页面配置
├── manifest.json           # 应用配置
└── uni.scss                # 全局样式
```

## 开发环境搭建

### 1. 安装开发工具
- 下载并安装 [HBuilderX](https://www.dcloud.io/hbuilderx.html)
- 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)

### 2. 项目配置
1. 在HBuilderX中打开项目
2. 修改 `manifest.json` 中的 `appid` 为你的微信小程序AppID
3. 配置后端API地址（在 `utils/request.js` 中修改 `BASE_URL`）

### 3. 运行项目
1. 在HBuilderX中点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
2. 首次运行会自动打开微信开发者工具
3. 在微信开发者工具中预览和调试

## API接口说明

### 基础配置
- 基础URL: `https://your-backend-domain.com/api`
- 认证方式: Bearer Token (JWT)
- 请求格式: JSON
- 响应格式: JSON

### 主要接口
- `POST /supplier/login` - 供应商登录
- `POST /supplier/wx_login` - 微信登录
- `GET /supplier/profile` - 获取供应商信息
- `GET /supplier/orders` - 获取订单列表
- `GET /supplier/products` - 获取产品列表
- `GET /supplier/schools` - 获取合作学校

详细API文档请参考后端接口文档。

## 部署说明

### 1. 代码上传
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传代码到微信服务器

### 2. 提交审核
1. 登录微信公众平台
2. 进入"版本管理"
3. 选择已上传的版本提交审核

### 3. 发布上线
1. 审核通过后点击"发布"
2. 选择发布方式（全量/分阶段）
3. 发布后用户可正常使用

## 注意事项

### 开发注意事项
1. 确保后端API支持跨域请求
2. 在微信公众平台配置服务器域名白名单
3. 测试时注意网络环境和权限设置
4. 遵循微信小程序开发规范

### 安全注意事项
1. 不要在代码中硬编码敏感信息
2. 使用HTTPS协议进行数据传输
3. 实现适当的权限验证和数据校验
4. 定期更新依赖和安全补丁

## 常见问题

### Q: 小程序无法正常登录？
A: 检查以下几点：
- 后端API是否正常运行
- 网络请求域名是否在白名单中
- AppID配置是否正确

### Q: 图片上传失败？
A: 检查：
- 上传接口是否正常
- 图片大小是否超限
- 文件格式是否支持

### Q: 页面跳转异常？
A: 检查：
- 页面路径是否正确
- pages.json配置是否完整
- 参数传递是否正确

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础功能模块
- 支持订单和产品管理
- 完成用户认证系统

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

本项目采用 MIT 许可证。
