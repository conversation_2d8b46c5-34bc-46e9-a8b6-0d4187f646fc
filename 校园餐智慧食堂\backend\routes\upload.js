const express = require('express');
const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const { createError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
const avatarDir = path.join(uploadDir, 'avatars');
const productDir = path.join(uploadDir, 'products');

[uploadDir, avatarDir, productDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// 配置multer
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(createError('不支持的文件类型，只允许上传 JPG、PNG、GIF 格式的图片', 'INVALID_FILE_TYPE', 400), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB
    files: 1
  }
});

// 图片处理函数
const processImage = async (buffer, options = {}) => {
  const {
    width = 800,
    height = 600,
    quality = 80,
    format = 'jpeg'
  } = options;

  try {
    let processor = sharp(buffer);

    // 获取图片信息
    const metadata = await processor.metadata();
    
    // 如果图片尺寸超过限制，进行缩放
    if (metadata.width > width || metadata.height > height) {
      processor = processor.resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }

    // 转换格式并压缩
    if (format === 'jpeg') {
      processor = processor.jpeg({ quality });
    } else if (format === 'png') {
      processor = processor.png({ quality });
    }

    return await processor.toBuffer();
  } catch (error) {
    throw createError('图片处理失败', 'IMAGE_PROCESS_ERROR', 500);
  }
};

// 生成文件名
const generateFileName = (originalName, type = 'image') => {
  const ext = path.extname(originalName).toLowerCase();
  const timestamp = Date.now();
  const uuid = uuidv4().substring(0, 8);
  return `${type}_${timestamp}_${uuid}${ext}`;
};

// 上传头像
router.post('/avatar', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    throw createError('请选择要上传的文件', 'NO_FILE_SELECTED', 400);
  }

  try {
    // 处理图片（头像特殊处理：正方形，较小尺寸）
    const processedBuffer = await processImage(req.file.buffer, {
      width: 300,
      height: 300,
      quality: 85,
      format: 'jpeg'
    });

    // 生成文件名
    const fileName = generateFileName(req.file.originalname, 'avatar');
    const filePath = path.join(avatarDir, fileName);

    // 保存文件
    fs.writeFileSync(filePath, processedBuffer);

    // 生成访问URL
    const fileUrl = `/uploads/avatars/${fileName}`;

    // 更新用户头像
    const { executeQuery } = require('../config/database');
    await executeQuery(
      'UPDATE suppliers SET avatar = ?, updated_at = NOW() WHERE id = ?',
      [fileUrl, req.user.id]
    );

    res.json({
      success: true,
      message: '头像上传成功',
      data: {
        url: fileUrl,
        filename: fileName,
        size: processedBuffer.length
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 上传产品图片
router.post('/product', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    throw createError('请选择要上传的文件', 'NO_FILE_SELECTED', 400);
  }

  try {
    // 处理图片（产品图片：保持比例，适中尺寸）
    const processedBuffer = await processImage(req.file.buffer, {
      width: 800,
      height: 600,
      quality: 80,
      format: 'jpeg'
    });

    // 生成文件名
    const fileName = generateFileName(req.file.originalname, 'product');
    const filePath = path.join(productDir, fileName);

    // 保存文件
    fs.writeFileSync(filePath, processedBuffer);

    // 生成访问URL
    const fileUrl = `/uploads/products/${fileName}`;

    res.json({
      success: true,
      message: '产品图片上传成功',
      data: {
        url: fileUrl,
        filename: fileName,
        size: processedBuffer.length
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 上传产品图片（指定产品ID）
router.post('/product/:id', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    throw createError('请选择要上传的文件', 'NO_FILE_SELECTED', 400);
  }

  const productId = req.params.id;

  try {
    // 验证产品是否属于当前供应商
    const { executeQuery } = require('../config/database');
    const products = await executeQuery(
      'SELECT id, product_name FROM products WHERE id = ? AND supplier_id = ?',
      [productId, req.user.id]
    );

    if (products.length === 0) {
      throw createError('产品不存在', 'PRODUCT_NOT_FOUND', 404);
    }

    // 处理图片
    const processedBuffer = await processImage(req.file.buffer, {
      width: 800,
      height: 600,
      quality: 80,
      format: 'jpeg'
    });

    // 生成文件名
    const fileName = generateFileName(req.file.originalname, `product_${productId}`);
    const filePath = path.join(productDir, fileName);

    // 保存文件
    fs.writeFileSync(filePath, processedBuffer);

    // 生成访问URL
    const fileUrl = `/uploads/products/${fileName}`;

    // 更新产品图片
    await executeQuery(
      'UPDATE products SET product_image = ?, updated_at = NOW() WHERE id = ?',
      [fileUrl, productId]
    );

    res.json({
      success: true,
      message: '产品图片上传成功',
      data: {
        url: fileUrl,
        filename: fileName,
        size: processedBuffer.length,
        product_id: productId,
        product_name: products[0].product_name
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 删除文件
router.delete('/file', asyncHandler(async (req, res) => {
  const { url } = req.body;

  if (!url) {
    throw createError('文件URL不能为空', 'MISSING_FILE_URL', 400);
  }

  try {
    // 从URL中提取文件路径
    const urlPath = url.replace(/^\/uploads\//, '');
    const filePath = path.join(uploadDir, urlPath);

    // 检查文件是否存在
    if (fs.existsSync(filePath)) {
      // 删除文件
      fs.unlinkSync(filePath);
    }

    res.json({
      success: true,
      message: '文件删除成功'
    });

  } catch (error) {
    throw createError('文件删除失败', 'FILE_DELETE_ERROR', 500);
  }
}));

// 获取文件信息
router.get('/info', asyncHandler(async (req, res) => {
  const { url } = req.query;

  if (!url) {
    throw createError('文件URL不能为空', 'MISSING_FILE_URL', 400);
  }

  try {
    // 从URL中提取文件路径
    const urlPath = url.replace(/^\/uploads\//, '');
    const filePath = path.join(uploadDir, urlPath);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw createError('文件不存在', 'FILE_NOT_FOUND', 404);
    }

    // 获取文件信息
    const stats = fs.statSync(filePath);
    const ext = path.extname(filePath).toLowerCase();

    res.json({
      success: true,
      message: '获取成功',
      data: {
        url: url,
        size: stats.size,
        created_at: stats.birthtime,
        modified_at: stats.mtime,
        extension: ext,
        type: ext.includes('jpg') || ext.includes('jpeg') || ext.includes('png') || ext.includes('gif') ? 'image' : 'file'
      }
    });

  } catch (error) {
    throw error;
  }
}));

module.exports = router;
