{"name": "campus-canteen-supplier-api", "version": "1.0.0", "description": "校园餐智慧食堂供应商端API服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest"}, "keywords": ["campus", "canteen", "supplier", "api", "wechat", "miniprogram"], "author": "Campus Canteen Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "moment": "^2.29.4", "axios": "^1.5.0", "sharp": "^0.32.5", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}