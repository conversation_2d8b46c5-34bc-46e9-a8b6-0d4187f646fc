const express = require('express');
const { query, param, body, validationResult } = require('express-validator');

const { executeQuery, executePagedQuery } = require('../config/database');
const { createError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取产品列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
  query('per_page').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('is_available').optional().isInt({ min: 0, max: 1 }).withMessage('上架状态必须是0或1'),
  query('shelf_status').optional().isInt({ min: 0, max: 2 }).withMessage('审核状态必须在0-2之间'),
  query('keyword').optional().isLength({ max: 100 }).withMessage('搜索关键词长度不能超过100个字符')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const {
    page = 1,
    per_page = 10,
    is_available,
    shelf_status,
    keyword
  } = req.query;

  try {
    let whereConditions = ['supplier_id = ?'];
    let queryParams = [req.user.id];

    // 上架状态筛选
    if (is_available !== undefined) {
      whereConditions.push('is_available = ?');
      queryParams.push(is_available);
    }

    // 审核状态筛选
    if (shelf_status !== undefined) {
      whereConditions.push('shelf_status = ?');
      queryParams.push(shelf_status);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(product_name LIKE ? OR product_code LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    const baseQuery = `
      SELECT 
        id,
        product_name,
        product_code,
        product_image,
        specification,
        price,
        unit,
        quality_cert,
        is_available,
        shelf_status,
        created_at,
        updated_at
      FROM products
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY created_at DESC
    `;

    const result = await executePagedQuery(baseQuery, queryParams, page, per_page);

    res.json({
      success: true,
      message: '获取成功',
      data: {
        items: result.data,
        pagination: result.pagination
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 获取产品详情
router.get('/:id', [
  param('id').isInt({ min: 1 }).withMessage('产品ID必须是大于0的整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const productId = req.params.id;

  try {
    const productQuery = `
      SELECT 
        id,
        product_name,
        product_code,
        product_image,
        specification,
        price,
        unit,
        quality_cert,
        is_available,
        shelf_status,
        shelf_reason,
        created_at,
        updated_at
      FROM products
      WHERE id = ? AND supplier_id = ?
    `;

    const products = await executeQuery(productQuery, [productId, req.user.id]);

    if (products.length === 0) {
      throw createError('产品不存在', 'PRODUCT_NOT_FOUND', 404);
    }

    res.json({
      success: true,
      message: '获取成功',
      data: products[0]
    });

  } catch (error) {
    throw error;
  }
}));

// 添加产品
router.post('/', [
  body('product_name').notEmpty().withMessage('产品名称不能为空').isLength({ max: 100 }).withMessage('产品名称长度不能超过100个字符'),
  body('product_code').notEmpty().withMessage('产品编码不能为空').isLength({ max: 50 }).withMessage('产品编码长度不能超过50个字符'),
  body('specification').notEmpty().withMessage('规格说明不能为空').isLength({ max: 200 }).withMessage('规格说明长度不能超过200个字符'),
  body('price').isFloat({ min: 0.01 }).withMessage('价格必须大于0'),
  body('unit').optional().isLength({ max: 20 }).withMessage('单位长度不能超过20个字符'),
  body('quality_cert').optional().isLength({ max: 200 }).withMessage('质量认证长度不能超过200个字符'),
  body('product_image').optional().isURL().withMessage('产品图片必须是有效的URL')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const {
    product_name,
    product_code,
    specification,
    price,
    unit = '件',
    quality_cert,
    product_image
  } = req.body;

  try {
    // 检查产品编码是否重复
    const existingProduct = await executeQuery(
      'SELECT id FROM products WHERE product_code = ? AND supplier_id = ?',
      [product_code, req.user.id]
    );

    if (existingProduct.length > 0) {
      throw createError('产品编码已存在', 'PRODUCT_CODE_EXISTS', 400);
    }

    // 插入新产品
    const insertQuery = `
      INSERT INTO products (
        supplier_id,
        product_name,
        product_code,
        product_image,
        specification,
        price,
        unit,
        quality_cert,
        is_available,
        shelf_status,
        created_at,
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, 0, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      req.user.id,
      product_name,
      product_code,
      product_image || null,
      specification,
      price,
      unit,
      quality_cert || null
    ]);

    // 获取新创建的产品信息
    const newProduct = await executeQuery(
      'SELECT * FROM products WHERE id = ?',
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '产品添加成功',
      data: newProduct[0]
    });

  } catch (error) {
    throw error;
  }
}));

// 更新产品
router.put('/:id', [
  param('id').isInt({ min: 1 }).withMessage('产品ID必须是大于0的整数'),
  body('product_name').optional().isLength({ min: 1, max: 100 }).withMessage('产品名称长度应在1-100个字符之间'),
  body('product_code').optional().isLength({ min: 1, max: 50 }).withMessage('产品编码长度应在1-50个字符之间'),
  body('specification').optional().isLength({ min: 1, max: 200 }).withMessage('规格说明长度应在1-200个字符之间'),
  body('price').optional().isFloat({ min: 0.01 }).withMessage('价格必须大于0'),
  body('unit').optional().isLength({ max: 20 }).withMessage('单位长度不能超过20个字符'),
  body('quality_cert').optional().isLength({ max: 200 }).withMessage('质量认证长度不能超过200个字符'),
  body('product_image').optional().isURL().withMessage('产品图片必须是有效的URL'),
  body('is_available').optional().isInt({ min: 0, max: 1 }).withMessage('上架状态必须是0或1')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const productId = req.params.id;

  try {
    // 检查产品是否存在
    const existingProduct = await executeQuery(
      'SELECT id, product_code FROM products WHERE id = ? AND supplier_id = ?',
      [productId, req.user.id]
    );

    if (existingProduct.length === 0) {
      throw createError('产品不存在', 'PRODUCT_NOT_FOUND', 404);
    }

    // 如果更新产品编码，检查是否重复
    if (req.body.product_code && req.body.product_code !== existingProduct[0].product_code) {
      const duplicateCheck = await executeQuery(
        'SELECT id FROM products WHERE product_code = ? AND supplier_id = ? AND id != ?',
        [req.body.product_code, req.user.id, productId]
      );

      if (duplicateCheck.length > 0) {
        throw createError('产品编码已存在', 'PRODUCT_CODE_EXISTS', 400);
      }
    }

    const allowedFields = [
      'product_name', 'product_code', 'specification', 'price', 
      'unit', 'quality_cert', 'product_image', 'is_available'
    ];

    const updateFields = [];
    const updateValues = [];

    // 构建更新字段
    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(req.body[field]);
      }
    }

    if (updateFields.length === 0) {
      throw createError('没有需要更新的字段', 'NO_UPDATE_FIELDS', 400);
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(productId);

    const updateQuery = `
      UPDATE products 
      SET ${updateFields.join(', ')} 
      WHERE id = ?
    `;

    await executeQuery(updateQuery, updateValues);

    // 获取更新后的产品信息
    const updatedProduct = await executeQuery(
      'SELECT * FROM products WHERE id = ?',
      [productId]
    );

    res.json({
      success: true,
      message: '产品更新成功',
      data: updatedProduct[0]
    });

  } catch (error) {
    throw error;
  }
}));

// 删除产品
router.delete('/:id', [
  param('id').isInt({ min: 1 }).withMessage('产品ID必须是大于0的整数')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const productId = req.params.id;

  try {
    // 检查产品是否存在
    const existingProduct = await executeQuery(
      'SELECT id FROM products WHERE id = ? AND supplier_id = ?',
      [productId, req.user.id]
    );

    if (existingProduct.length === 0) {
      throw createError('产品不存在', 'PRODUCT_NOT_FOUND', 404);
    }

    // 检查是否有相关订单
    const orderCheck = await executeQuery(
      'SELECT id FROM order_items WHERE product_id = ? LIMIT 1',
      [productId]
    );

    if (orderCheck.length > 0) {
      throw createError('该产品已有相关订单，无法删除', 'PRODUCT_HAS_ORDERS', 400);
    }

    // 删除产品
    await executeQuery(
      'DELETE FROM products WHERE id = ?',
      [productId]
    );

    res.json({
      success: true,
      message: '产品删除成功'
    });

  } catch (error) {
    throw error;
  }
}));

// 切换产品上架状态
router.patch('/:id/toggle_status', [
  param('id').isInt({ min: 1 }).withMessage('产品ID必须是大于0的整数'),
  body('is_available').isInt({ min: 0, max: 1 }).withMessage('上架状态必须是0或1')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const productId = req.params.id;
  const { is_available } = req.body;

  try {
    // 检查产品是否存在
    const existingProduct = await executeQuery(
      'SELECT id, product_name FROM products WHERE id = ? AND supplier_id = ?',
      [productId, req.user.id]
    );

    if (existingProduct.length === 0) {
      throw createError('产品不存在', 'PRODUCT_NOT_FOUND', 404);
    }

    // 更新状态
    await executeQuery(
      'UPDATE products SET is_available = ?, updated_at = NOW() WHERE id = ?',
      [is_available, productId]
    );

    res.json({
      success: true,
      message: `产品已${is_available ? '上架' : '下架'}`,
      data: {
        product_id: productId,
        product_name: existingProduct[0].product_name,
        is_available: is_available
      }
    });

  } catch (error) {
    throw error;
  }
}));

module.exports = router;
