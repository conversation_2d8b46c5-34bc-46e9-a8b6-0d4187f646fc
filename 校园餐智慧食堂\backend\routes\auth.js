const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const axios = require('axios');

const { executeQuery } = require('../config/database');
const { generateToken } = require('../middleware/auth');
const { createError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 供应商登录
router.post('/login', [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度应在3-50个字符之间'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符')
], asyncHandler(async (req, res) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const { username, password } = req.body;

  try {
    // 查询供应商信息
    const supplierQuery = `
      SELECT 
        id,
        username,
        password,
        name,
        phone,
        email,
        avatar,
        status,
        contact_person,
        address,
        business_license,
        created_at,
        updated_at
      FROM suppliers 
      WHERE username = ?
    `;

    const suppliers = await executeQuery(supplierQuery, [username]);

    if (suppliers.length === 0) {
      throw createError('用户名或密码错误', 'INVALID_CREDENTIALS', 401);
    }

    const supplier = suppliers[0];

    // 检查账户状态
    if (supplier.status !== 1) {
      throw createError('账户已被禁用，请联系管理员', 'ACCOUNT_DISABLED', 403);
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, supplier.password);
    if (!isPasswordValid) {
      throw createError('用户名或密码错误', 'INVALID_CREDENTIALS', 401);
    }

    // 生成JWT token
    const token = generateToken(supplier.id);

    // 更新最后登录时间
    await executeQuery(
      'UPDATE suppliers SET last_login_at = NOW() WHERE id = ?',
      [supplier.id]
    );

    // 获取供应商统计信息
    const statsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 1) as pending_orders,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status IN (2, 3)) as processing_orders,
        (SELECT COUNT(*) FROM products WHERE supplier_id = ?) as total_products,
        (SELECT COUNT(DISTINCT school_id) FROM supplier_schools WHERE supplier_id = ? AND status = 1) as cooperating_schools,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE supplier_id = ? AND DATE(created_at) = CURDATE() AND status = 4) as today_revenue
    `;

    const stats = await executeQuery(statsQuery, [
      supplier.id, supplier.id, supplier.id, supplier.id, supplier.id
    ]);

    // 返回登录成功信息
    res.json({
      success: true,
      message: '登录成功',
      data: {
        access_token: token,
        token_type: 'Bearer',
        expires_in: 7 * 24 * 60 * 60, // 7天，单位：秒
        user_info: {
          id: supplier.id,
          username: supplier.username,
          name: supplier.name,
          phone: supplier.phone,
          email: supplier.email,
          avatar: supplier.avatar,
          status: supplier.status,
          contact_person: supplier.contact_person,
          address: supplier.address,
          business_license: supplier.business_license
        },
        statistics: stats[0] || {
          pending_orders: 0,
          processing_orders: 0,
          total_products: 0,
          cooperating_schools: 0,
          today_revenue: 0
        }
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 微信登录
router.post('/wx_login', [
  body('code')
    .notEmpty()
    .withMessage('微信登录code不能为空')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const { code } = req.body;

  try {
    // 调用微信API获取openid
    const wechatResponse = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
      params: {
        appid: process.env.WECHAT_APP_ID,
        secret: process.env.WECHAT_APP_SECRET,
        js_code: code,
        grant_type: 'authorization_code'
      }
    });

    if (wechatResponse.data.errcode) {
      throw createError('微信登录失败', 'WECHAT_LOGIN_ERROR', 400, {
        errcode: wechatResponse.data.errcode,
        errmsg: wechatResponse.data.errmsg
      });
    }

    const { openid, session_key } = wechatResponse.data;

    // 查询是否已绑定供应商账号
    const bindQuery = `
      SELECT 
        s.id,
        s.username,
        s.name,
        s.phone,
        s.email,
        s.avatar,
        s.status,
        s.contact_person,
        s.address,
        s.business_license
      FROM suppliers s
      INNER JOIN supplier_wechat sw ON s.id = sw.supplier_id
      WHERE sw.openid = ? AND s.status = 1
    `;

    const boundSuppliers = await executeQuery(bindQuery, [openid]);

    if (boundSuppliers.length === 0) {
      // 未绑定，返回需要绑定的信息
      return res.json({
        success: true,
        message: '需要绑定供应商账号',
        data: {
          need_bind: true,
          openid: openid,
          session_key: session_key
        }
      });
    }

    const supplier = boundSuppliers[0];

    // 生成JWT token
    const token = generateToken(supplier.id);

    // 更新最后登录时间
    await executeQuery(
      'UPDATE suppliers SET last_login_at = NOW() WHERE id = ?',
      [supplier.id]
    );

    // 获取统计信息
    const statsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 1) as pending_orders,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status IN (2, 3)) as processing_orders,
        (SELECT COUNT(*) FROM products WHERE supplier_id = ?) as total_products,
        (SELECT COUNT(DISTINCT school_id) FROM supplier_schools WHERE supplier_id = ? AND status = 1) as cooperating_schools,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE supplier_id = ? AND DATE(created_at) = CURDATE() AND status = 4) as today_revenue
    `;

    const stats = await executeQuery(statsQuery, [
      supplier.id, supplier.id, supplier.id, supplier.id, supplier.id
    ]);

    res.json({
      success: true,
      message: '微信登录成功',
      data: {
        access_token: token,
        token_type: 'Bearer',
        expires_in: 7 * 24 * 60 * 60,
        user_info: {
          id: supplier.id,
          username: supplier.username,
          name: supplier.name,
          phone: supplier.phone,
          email: supplier.email,
          avatar: supplier.avatar,
          status: supplier.status,
          contact_person: supplier.contact_person,
          address: supplier.address,
          business_license: supplier.business_license,
          wechat_openid: openid
        },
        statistics: stats[0] || {
          pending_orders: 0,
          processing_orders: 0,
          total_products: 0,
          cooperating_schools: 0,
          today_revenue: 0
        }
      }
    });

  } catch (error) {
    if (error.isCustomError) {
      throw error;
    }
    throw createError('微信登录服务异常', 'WECHAT_SERVICE_ERROR', 500);
  }
}));

// 绑定微信账号
router.post('/bind_account', [
  body('openid').notEmpty().withMessage('openid不能为空'),
  body('username').notEmpty().withMessage('用户名不能为空'),
  body('password').notEmpty().withMessage('密码不能为空')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const { openid, username, password } = req.body;

  try {
    // 验证供应商账号
    const supplierQuery = `
      SELECT id, username, password, name, phone, email, avatar, status, contact_person, address, business_license
      FROM suppliers 
      WHERE username = ?
    `;

    const suppliers = await executeQuery(supplierQuery, [username]);

    if (suppliers.length === 0) {
      throw createError('用户名或密码错误', 'INVALID_CREDENTIALS', 401);
    }

    const supplier = suppliers[0];

    if (supplier.status !== 1) {
      throw createError('账户已被禁用', 'ACCOUNT_DISABLED', 403);
    }

    const isPasswordValid = await bcrypt.compare(password, supplier.password);
    if (!isPasswordValid) {
      throw createError('用户名或密码错误', 'INVALID_CREDENTIALS', 401);
    }

    // 检查是否已绑定其他微信账号
    const existingBindQuery = `
      SELECT id FROM supplier_wechat WHERE supplier_id = ?
    `;
    const existingBinds = await executeQuery(existingBindQuery, [supplier.id]);

    if (existingBinds.length > 0) {
      throw createError('该供应商账号已绑定其他微信账号', 'ALREADY_BOUND', 400);
    }

    // 检查openid是否已绑定其他账号
    const openidBindQuery = `
      SELECT id FROM supplier_wechat WHERE openid = ?
    `;
    const openidBinds = await executeQuery(openidBindQuery, [openid]);

    if (openidBinds.length > 0) {
      throw createError('该微信账号已绑定其他供应商账号', 'OPENID_ALREADY_BOUND', 400);
    }

    // 创建绑定关系
    await executeQuery(
      'INSERT INTO supplier_wechat (supplier_id, openid, created_at) VALUES (?, ?, NOW())',
      [supplier.id, openid]
    );

    // 生成token
    const token = generateToken(supplier.id);

    // 更新最后登录时间
    await executeQuery(
      'UPDATE suppliers SET last_login_at = NOW() WHERE id = ?',
      [supplier.id]
    );

    res.json({
      success: true,
      message: '绑定成功',
      data: {
        access_token: token,
        token_type: 'Bearer',
        expires_in: 7 * 24 * 60 * 60,
        user_info: {
          id: supplier.id,
          username: supplier.username,
          name: supplier.name,
          phone: supplier.phone,
          email: supplier.email,
          avatar: supplier.avatar,
          status: supplier.status,
          contact_person: supplier.contact_person,
          address: supplier.address,
          business_license: supplier.business_license,
          wechat_openid: openid
        }
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 退出登录
router.post('/logout', asyncHandler(async (req, res) => {
  // 在实际应用中，可以在这里实现token黑名单机制
  // 目前只是简单返回成功信息
  res.json({
    success: true,
    message: '退出登录成功'
  });
}));

module.exports = router;
