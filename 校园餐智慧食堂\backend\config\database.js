const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  // 连接池配置
  connectionLimit: 10,
  queueLimit: 0,
  // SQL模式配置
  sql_mode: 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    console.log(`📍 连接到: ${process.env.DB_HOST}:${process.env.DB_PORT}`);
    console.log(`🗄️  数据库: ${process.env.DB_NAME}`);
    
    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 数据库查询测试成功');
    
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

// 执行查询的封装函数
async function executeQuery(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
}

// 执行事务的封装函数
async function executeTransaction(queries) {
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const { sql, params } of queries) {
      const [rows] = await connection.execute(sql, params || []);
      results.push(rows);
    }
    
    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

// 分页查询封装
async function executePagedQuery(sql, params = [], page = 1, pageSize = 10) {
  try {
    // 计算偏移量
    const offset = (page - 1) * pageSize;
    
    // 构建分页SQL
    const pagedSql = `${sql} LIMIT ${pageSize} OFFSET ${offset}`;
    
    // 构建计数SQL
    const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_table`;
    
    // 并行执行查询和计数
    const [dataRows, countRows] = await Promise.all([
      pool.execute(pagedSql, params),
      pool.execute(countSql, params)
    ]);
    
    const total = countRows[0][0].total;
    const totalPages = Math.ceil(total / pageSize);
    
    return {
      data: dataRows[0],
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: parseInt(total),
        totalPages: parseInt(totalPages),
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('分页查询错误:', error);
    throw error;
  }
}

// 关闭连接池
async function closePool() {
  try {
    await pool.end();
    console.log('✅ 数据库连接池已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接池失败:', error);
  }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('\n🔄 正在关闭数据库连接...');
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🔄 正在关闭数据库连接...');
  await closePool();
  process.exit(0);
});

module.exports = {
  pool,
  testConnection,
  executeQuery,
  executeTransaction,
  executePagedQuery,
  closePool
};
