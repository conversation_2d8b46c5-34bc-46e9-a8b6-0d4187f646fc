<template>
  <view class="settings-container">
    <!-- 账户设置 -->
    <view class="settings-section">
      <view class="section-title">
        <text class="title-text">账户设置</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item" @click="editProfile">
          <view class="setting-left">
            <text class="setting-icon">👤</text>
            <text class="setting-text">个人信息</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">编辑资料</text>
            <text class="setting-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @click="changePassword">
          <view class="setting-left">
            <text class="setting-icon">🔒</text>
            <text class="setting-text">修改密码</text>
          </view>
          <view class="setting-right">
            <text class="setting-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @click="bindWechat">
          <view class="setting-left">
            <text class="setting-icon">💬</text>
            <text class="setting-text">微信绑定</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ wechatBound ? '已绑定' : '未绑定' }}</text>
            <text class="setting-arrow">></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 通知设置 -->
    <view class="settings-section">
      <view class="section-title">
        <text class="title-text">通知设置</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">🔔</text>
            <text class="setting-text">订单通知</text>
          </view>
          <view class="setting-right">
            <switch 
              :checked="settings.orderNotification" 
              @change="handleSettingChange('orderNotification', $event)"
              color="#007AFF"
            />
          </view>
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">📦</text>
            <text class="setting-text">产品通知</text>
          </view>
          <view class="setting-right">
            <switch 
              :checked="settings.productNotification" 
              @change="handleSettingChange('productNotification', $event)"
              color="#007AFF"
            />
          </view>
        </view>
        
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-icon">💰</text>
            <text class="setting-text">收款通知</text>
          </view>
          <view class="setting-right">
            <switch 
              :checked="settings.paymentNotification" 
              @change="handleSettingChange('paymentNotification', $event)"
              color="#007AFF"
            />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 数据设置 -->
    <view class="settings-section">
      <view class="section-title">
        <text class="title-text">数据管理</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item" @click="exportData">
          <view class="setting-left">
            <text class="setting-icon">📊</text>
            <text class="setting-text">数据导出</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">导出业务数据</text>
            <text class="setting-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @click="clearCache">
          <view class="setting-left">
            <text class="setting-icon">🗑️</text>
            <text class="setting-text">清除缓存</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">{{ cacheSize }}</text>
            <text class="setting-arrow">></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 其他设置 -->
    <view class="settings-section">
      <view class="section-title">
        <text class="title-text">其他</text>
      </view>
      
      <view class="settings-group">
        <view class="setting-item" @click="checkUpdate">
          <view class="setting-left">
            <text class="setting-icon">🔄</text>
            <text class="setting-text">检查更新</text>
          </view>
          <view class="setting-right">
            <text class="setting-value">v1.0.0</text>
            <text class="setting-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @click="feedback">
          <view class="setting-left">
            <text class="setting-icon">💬</text>
            <text class="setting-text">意见反馈</text>
          </view>
          <view class="setting-right">
            <text class="setting-arrow">></text>
          </view>
        </view>
        
        <view class="setting-item" @click="about">
          <view class="setting-left">
            <text class="setting-icon">ℹ️</text>
            <text class="setting-text">关于我们</text>
          </view>
          <view class="setting-right">
            <text class="setting-arrow">></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { configAPI } from '@/api/supplier.js';
import { showError, showSuccess, showLoading, hideLoading, showConfirm } from '@/utils/request.js';

export default {
  data() {
    return {
      settings: {
        orderNotification: true,
        productNotification: true,
        paymentNotification: true
      },
      wechatBound: false,
      cacheSize: '0KB'
    };
  },
  
  onLoad() {
    this.loadSettings();
    this.calculateCacheSize();
  },
  
  methods: {
    // 加载设置
    async loadSettings() {
      try {
        const savedSettings = uni.getStorageSync('appSettings');
        if (savedSettings) {
          this.settings = { ...this.settings, ...savedSettings };
        }
        
        // 检查微信绑定状态
        const userInfo = uni.getStorageSync('userInfo');
        this.wechatBound = !!(userInfo && userInfo.wechat_openid);
        
      } catch (error) {
        console.error('加载设置失败:', error);
      }
    },
    
    // 保存设置
    saveSettings() {
      try {
        uni.setStorageSync('appSettings', this.settings);
      } catch (error) {
        console.error('保存设置失败:', error);
      }
    },
    
    // 设置项变化
    handleSettingChange(key, event) {
      this.settings[key] = event.detail.value;
      this.saveSettings();
      
      showSuccess(event.detail.value ? '已开启通知' : '已关闭通知');
    },
    
    // 编辑个人资料
    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      });
    },
    
    // 修改密码
    changePassword() {
      uni.navigateTo({
        url: '/pages/settings/password'
      });
    },
    
    // 绑定微信
    bindWechat() {
      if (this.wechatBound) {
        uni.showActionSheet({
          itemList: ['解除绑定'],
          success: (res) => {
            if (res.tapIndex === 0) {
              this.unbindWechat();
            }
          }
        });
      } else {
        uni.navigateTo({
          url: '/pages/settings/bind-wechat'
        });
      }
    },
    
    // 解除微信绑定
    async unbindWechat() {
      const confirmed = await showConfirm('确认解除微信绑定？');
      if (!confirmed) return;
      
      showLoading('处理中...');
      
      try {
        // 调用解绑API
        // await authAPI.unbindWechat();
        
        this.wechatBound = false;
        showSuccess('已解除微信绑定');
        
      } catch (error) {
        showError(error.message || '操作失败');
      } finally {
        hideLoading();
      }
    },
    
    // 数据导出
    exportData() {
      uni.navigateTo({
        url: '/pages/settings/export'
      });
    },
    
    // 清除缓存
    async clearCache() {
      const confirmed = await showConfirm('确认清除所有缓存数据？');
      if (!confirmed) return;
      
      showLoading('清除中...');
      
      try {
        // 清除除了登录信息外的所有缓存
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');
        const appSettings = uni.getStorageSync('appSettings');
        
        uni.clearStorageSync();
        
        // 恢复重要数据
        if (token) uni.setStorageSync('token', token);
        if (userInfo) uni.setStorageSync('userInfo', userInfo);
        if (appSettings) uni.setStorageSync('appSettings', appSettings);
        
        this.calculateCacheSize();
        showSuccess('缓存已清除');
        
      } catch (error) {
        showError('清除缓存失败');
      } finally {
        hideLoading();
      }
    },
    
    // 计算缓存大小
    calculateCacheSize() {
      try {
        const storageInfo = uni.getStorageInfoSync();
        const sizeKB = Math.round(storageInfo.currentSize);
        
        if (sizeKB < 1024) {
          this.cacheSize = sizeKB + 'KB';
        } else {
          this.cacheSize = (sizeKB / 1024).toFixed(1) + 'MB';
        }
      } catch (error) {
        this.cacheSize = '0KB';
      }
    },
    
    // 检查更新
    async checkUpdate() {
      showLoading('检查中...');
      
      try {
        // 模拟检查更新
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        showSuccess('当前已是最新版本');
        
      } catch (error) {
        showError('检查更新失败');
      } finally {
        hideLoading();
      }
    },
    
    // 意见反馈
    feedback() {
      uni.navigateTo({
        url: '/pages/help/feedback'
      });
    },
    
    // 关于我们
    about() {
      uni.navigateTo({
        url: '/pages/about/index'
      });
    }
  }
};
</script>

<style scoped>
.settings-container {
  background: #f8f8f8;
  min-height: 100vh;
}

.settings-section {
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx;
  background: #f8f8f8;
}

.title-text {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.settings-group {
  background: white;
  border-radius: 16rpx;
  margin: 0 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.setting-text {
  font-size: 28rpx;
  color: #333;
}

.setting-right {
  display: flex;
  align-items: center;
}

.setting-value {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.setting-arrow {
  font-size: 28rpx;
  color: #999;
}
</style>
