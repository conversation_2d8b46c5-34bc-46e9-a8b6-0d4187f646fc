const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');

const { executeQuery } = require('../config/database');
const { createError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取供应商信息
router.get('/profile', asyncHandler(async (req, res) => {
  try {
    const supplierQuery = `
      SELECT 
        id,
        username,
        name,
        phone,
        email,
        avatar,
        status,
        contact_person,
        address,
        business_license,
        tax_id,
        bank_name,
        bank_account,
        rating,
        created_at,
        updated_at,
        last_login_at
      FROM suppliers 
      WHERE id = ?
    `;

    const suppliers = await executeQuery(supplierQuery, [req.user.id]);

    if (suppliers.length === 0) {
      throw createError('供应商信息不存在', 'SUPPLIER_NOT_FOUND', 404);
    }

    const supplier = suppliers[0];

    // 检查是否绑定微信
    const wechatQuery = `
      SELECT openid FROM supplier_wechat WHERE supplier_id = ?
    `;
    const wechatBinds = await executeQuery(wechatQuery, [req.user.id]);

    res.json({
      success: true,
      message: '获取成功',
      data: {
        ...supplier,
        wechat_openid: wechatBinds.length > 0 ? wechatBinds[0].openid : null
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 更新供应商信息
router.put('/profile', [
  body('name').optional().isLength({ min: 1, max: 100 }).withMessage('供应商名称长度应在1-100个字符之间'),
  body('phone').optional().isMobilePhone('zh-CN').withMessage('请输入正确的手机号'),
  body('email').optional().isEmail().withMessage('请输入正确的邮箱地址'),
  body('contact_person').optional().isLength({ min: 1, max: 50 }).withMessage('联系人姓名长度应在1-50个字符之间'),
  body('address').optional().isLength({ max: 200 }).withMessage('地址长度不能超过200个字符'),
  body('business_license').optional().isLength({ max: 50 }).withMessage('营业执照号长度不能超过50个字符'),
  body('tax_id').optional().isLength({ max: 50 }).withMessage('税务登记号长度不能超过50个字符'),
  body('bank_name').optional().isLength({ max: 100 }).withMessage('银行名称长度不能超过100个字符'),
  body('bank_account').optional().isLength({ max: 50 }).withMessage('银行账号长度不能超过50个字符')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const allowedFields = [
    'name', 'phone', 'email', 'contact_person', 'address', 
    'business_license', 'tax_id', 'bank_name', 'bank_account', 'avatar'
  ];

  const updateFields = [];
  const updateValues = [];

  // 构建更新字段
  for (const field of allowedFields) {
    if (req.body[field] !== undefined) {
      updateFields.push(`${field} = ?`);
      updateValues.push(req.body[field]);
    }
  }

  if (updateFields.length === 0) {
    throw createError('没有需要更新的字段', 'NO_UPDATE_FIELDS', 400);
  }

  updateFields.push('updated_at = NOW()');
  updateValues.push(req.user.id);

  try {
    const updateQuery = `
      UPDATE suppliers 
      SET ${updateFields.join(', ')} 
      WHERE id = ?
    `;

    await executeQuery(updateQuery, updateValues);

    // 获取更新后的信息
    const updatedSupplier = await executeQuery(
      'SELECT id, username, name, phone, email, avatar, contact_person, address, business_license, tax_id, bank_name, bank_account FROM suppliers WHERE id = ?',
      [req.user.id]
    );

    res.json({
      success: true,
      message: '更新成功',
      data: updatedSupplier[0]
    });

  } catch (error) {
    throw error;
  }
}));

// 获取统计数据
router.get('/statistics', asyncHandler(async (req, res) => {
  try {
    const supplierId = req.user.id;

    // 获取基础统计数据
    const basicStatsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 1) as pending_orders,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 2) as accepted_orders,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 3) as shipping_orders,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 4) as completed_orders,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ? AND status = 5) as cancelled_orders,
        (SELECT COUNT(*) FROM orders WHERE supplier_id = ?) as total_orders,
        (SELECT COUNT(*) FROM products WHERE supplier_id = ?) as total_products,
        (SELECT COUNT(*) FROM products WHERE supplier_id = ? AND is_available = 1) as available_products,
        (SELECT COUNT(DISTINCT school_id) FROM supplier_schools WHERE supplier_id = ? AND status = 1) as cooperating_schools
    `;

    const basicStats = await executeQuery(basicStatsQuery, [
      supplierId, supplierId, supplierId, supplierId, supplierId, 
      supplierId, supplierId, supplierId, supplierId
    ]);

    // 获取收入统计
    const revenueStatsQuery = `
      SELECT 
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE supplier_id = ? AND DATE(created_at) = CURDATE() AND status = 4) as today_revenue,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE supplier_id = ? AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND status = 4) as week_revenue,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE supplier_id = ? AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND status = 4) as month_revenue,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE supplier_id = ? AND status = 4) as total_revenue
    `;

    const revenueStats = await executeQuery(revenueStatsQuery, [
      supplierId, supplierId, supplierId, supplierId
    ]);

    // 获取昨日收入用于计算趋势
    const yesterdayRevenueQuery = `
      SELECT COALESCE(SUM(total_amount), 0) as yesterday_revenue
      FROM orders 
      WHERE supplier_id = ? AND DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND status = 4
    `;

    const yesterdayRevenue = await executeQuery(yesterdayRevenueQuery, [supplierId]);

    // 计算收入趋势
    const todayRevenue = revenueStats[0].today_revenue || 0;
    const yesterdayRev = yesterdayRevenue[0].yesterday_revenue || 0;
    let revenueTrend = 0;

    if (yesterdayRev > 0) {
      revenueTrend = ((todayRevenue - yesterdayRev) / yesterdayRev * 100);
    } else if (todayRevenue > 0) {
      revenueTrend = 100;
    }

    // 获取最近7天的收入趋势数据
    const trendQuery = `
      SELECT 
        DATE(created_at) as date,
        COALESCE(SUM(total_amount), 0) as revenue
      FROM orders 
      WHERE supplier_id = ? 
        AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        AND status = 4
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;

    const trendData = await executeQuery(trendQuery, [supplierId]);

    res.json({
      success: true,
      message: '获取成功',
      data: {
        // 订单统计
        pendingOrders: basicStats[0].pending_orders || 0,
        acceptedOrders: basicStats[0].accepted_orders || 0,
        shippingOrders: basicStats[0].shipping_orders || 0,
        completedOrders: basicStats[0].completed_orders || 0,
        cancelledOrders: basicStats[0].cancelled_orders || 0,
        totalOrders: basicStats[0].total_orders || 0,
        processingOrders: (basicStats[0].accepted_orders || 0) + (basicStats[0].shipping_orders || 0),
        
        // 产品统计
        totalProducts: basicStats[0].total_products || 0,
        availableProducts: basicStats[0].available_products || 0,
        
        // 学校统计
        cooperatingSchools: basicStats[0].cooperating_schools || 0,
        
        // 收入统计
        todayRevenue: todayRevenue,
        weekRevenue: revenueStats[0].week_revenue || 0,
        monthRevenue: revenueStats[0].month_revenue || 0,
        totalRevenue: revenueStats[0].total_revenue || 0,
        revenueTrend: Math.round(revenueTrend * 10) / 10, // 保留一位小数
        
        // 趋势数据
        revenueChart: trendData
      }
    });

  } catch (error) {
    throw error;
  }
}));

// 修改密码
router.put('/password', [
  body('old_password').notEmpty().withMessage('原密码不能为空'),
  body('new_password').isLength({ min: 6 }).withMessage('新密码长度至少6个字符'),
  body('confirm_password').custom((value, { req }) => {
    if (value !== req.body.new_password) {
      throw new Error('确认密码与新密码不一致');
    }
    return true;
  })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('参数验证失败', 'VALIDATION_ERROR', 400, errors.array());
  }

  const { old_password, new_password } = req.body;

  try {
    // 获取当前密码
    const passwordQuery = 'SELECT password FROM suppliers WHERE id = ?';
    const suppliers = await executeQuery(passwordQuery, [req.user.id]);

    if (suppliers.length === 0) {
      throw createError('用户不存在', 'USER_NOT_FOUND', 404);
    }

    // 验证原密码
    const isOldPasswordValid = await bcrypt.compare(old_password, suppliers[0].password);
    if (!isOldPasswordValid) {
      throw createError('原密码错误', 'INVALID_OLD_PASSWORD', 400);
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(new_password, 12);

    // 更新密码
    await executeQuery(
      'UPDATE suppliers SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedNewPassword, req.user.id]
    );

    res.json({
      success: true,
      message: '密码修改成功'
    });

  } catch (error) {
    throw error;
  }
}));

// 解除微信绑定
router.delete('/wechat_bind', asyncHandler(async (req, res) => {
  try {
    const result = await executeQuery(
      'DELETE FROM supplier_wechat WHERE supplier_id = ?',
      [req.user.id]
    );

    if (result.affectedRows === 0) {
      throw createError('未找到微信绑定记录', 'WECHAT_BIND_NOT_FOUND', 404);
    }

    res.json({
      success: true,
      message: '微信绑定已解除'
    });

  } catch (error) {
    throw error;
  }
}));

module.exports = router;
