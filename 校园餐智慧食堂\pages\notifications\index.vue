<template>
  <view class="notifications-container">
    <!-- 头部操作栏 -->
    <view class="header-actions">
      <view class="filter-tabs">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index"
          class="tab-item"
          :class="{ 'active': currentTab === index }"
          @click="handleTabChange(index)"
        >
          <text class="tab-text">{{ tab.name }}</text>
          <text v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</text>
        </view>
      </view>
      
      <view class="action-buttons">
        <button 
          v-if="unreadCount > 0" 
          class="mark-all-btn"
          @click="markAllAsRead"
        >
          全部已读
        </button>
      </view>
    </view>
    
    <!-- 消息列表 -->
    <view class="notification-list">
      <view v-if="loading && notificationList.length === 0" class="loading-state">
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="notificationList.length === 0" class="empty-state">
        <text class="empty-icon">📬</text>
        <text class="empty-text">暂无消息</text>
      </view>
      
      <view v-else>
        <view 
          v-for="(notification, index) in notificationList" 
          :key="index" 
          class="notification-item"
          :class="{ 'unread': !notification.is_read }"
          @click="handleNotificationClick(notification)"
        >
          <!-- 消息图标 -->
          <view class="notification-icon">
            <text class="icon-text">{{ getNotificationIcon(notification.type) }}</text>
            <view v-if="!notification.is_read" class="unread-dot"></view>
          </view>
          
          <!-- 消息内容 -->
          <view class="notification-content">
            <view class="notification-header">
              <text class="notification-title">{{ notification.title }}</text>
              <text class="notification-time">{{ getRelativeTime(notification.created_at) }}</text>
            </view>
            
            <view class="notification-body">
              <text class="notification-text">{{ notification.content }}</text>
            </view>
            
            <view v-if="notification.extra_data" class="notification-extra">
              <text class="extra-text">{{ getExtraText(notification) }}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="notification-actions">
            <button 
              v-if="!notification.is_read"
              class="action-btn read"
              @click.stop="markAsRead(notification.id)"
            >
              标记已读
            </button>
            <button 
              v-if="notification.action_url"
              class="action-btn primary"
              @click.stop="handleAction(notification)"
            >
              查看详情
            </button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view v-if="notificationList.length > 0" class="load-more">
      <text v-if="loadMoreStatus === 'loading'" class="load-text">加载中...</text>
      <text v-else-if="loadMoreStatus === 'nomore'" class="load-text">没有更多了</text>
      <text v-else class="load-text">上拉加载更多</text>
    </view>
  </view>
</template>

<script>
import { notificationAPI } from '@/api/supplier.js';
import { getRelativeTime } from '@/utils/common.js';
import { showError, showSuccess, showLoading, hideLoading, showConfirm } from '@/utils/request.js';

export default {
  data() {
    return {
      tabs: [
        { name: '全部', type: '', count: 0 },
        { name: '订单', type: 'order', count: 0 },
        { name: '产品', type: 'product', count: 0 },
        { name: '系统', type: 'system', count: 0 }
      ],
      currentTab: 0,
      notificationList: [],
      unreadCount: 0,
      page: 1,
      pageSize: 10,
      loading: false,
      loadMoreStatus: 'loadmore', // loading, loadmore, nomore
      refreshing: false
    };
  },
  
  onLoad() {
    this.loadNotificationList();
    this.loadUnreadCount();
  },
  
  onShow() {
    // 刷新未读数量
    this.loadUnreadCount();
  },
  
  onPullDownRefresh() {
    this.refreshData();
  },
  
  onReachBottom() {
    if (this.loadMoreStatus === 'loadmore') {
      this.loadMore();
    }
  },
  
  methods: {
    // 切换标签
    handleTabChange(index) {
      if (this.currentTab === index) return;
      
      this.currentTab = index;
      this.page = 1;
      this.notificationList = [];
      this.loadNotificationList();
    },
    
    // 加载消息列表
    async loadNotificationList() {
      if (this.loading) return;
      
      this.loading = true;
      if (this.page === 1) {
        showLoading('加载中...');
      }
      
      try {
        const params = {
          page: this.page,
          per_page: this.pageSize
        };
        
        // 添加类型筛选
        const currentType = this.tabs[this.currentTab].type;
        if (currentType) {
          params.type = currentType;
        }
        
        const response = await notificationAPI.getNotifications(params);
        
        if (this.page === 1) {
          this.notificationList = response.items || [];
        } else {
          this.notificationList = [...this.notificationList, ...(response.items || [])];
        }
        
        // 更新加载状态
        if (response.items && response.items.length < this.pageSize) {
          this.loadMoreStatus = 'nomore';
        } else {
          this.loadMoreStatus = 'loadmore';
        }
        
      } catch (error) {
        showError(error.message || '加载失败');
      } finally {
        this.loading = false;
        hideLoading();
        if (this.refreshing) {
          uni.stopPullDownRefresh();
          this.refreshing = false;
        }
      }
    },
    
    // 加载未读数量
    async loadUnreadCount() {
      try {
        const result = await notificationAPI.getUnreadCount();
        this.unreadCount = result.count || 0;
      } catch (error) {
        console.error('加载未读数量失败:', error);
      }
    },
    
    // 刷新数据
    refreshData() {
      this.refreshing = true;
      this.page = 1;
      this.notificationList = [];
      this.loadNotificationList();
      this.loadUnreadCount();
    },
    
    // 加载更多
    loadMore() {
      this.page++;
      this.loadNotificationList();
    },
    
    // 点击消息
    handleNotificationClick(notification) {
      // 如果未读，先标记为已读
      if (!notification.is_read) {
        this.markAsRead(notification.id);
      }
      
      // 如果有操作链接，执行操作
      if (notification.action_url) {
        this.handleAction(notification);
      }
    },
    
    // 标记单个消息为已读
    async markAsRead(id) {
      try {
        await notificationAPI.markAsRead(id);
        
        // 更新本地状态
        const notification = this.notificationList.find(item => item.id === id);
        if (notification) {
          notification.is_read = true;
        }
        
        // 更新未读数量
        this.loadUnreadCount();
        
      } catch (error) {
        showError(error.message || '操作失败');
      }
    },
    
    // 标记全部为已读
    async markAllAsRead() {
      const confirmed = await showConfirm('确认将所有消息标记为已读？');
      if (!confirmed) return;
      
      showLoading('处理中...');
      
      try {
        await notificationAPI.markAllAsRead();
        
        // 更新本地状态
        this.notificationList.forEach(item => {
          item.is_read = true;
        });
        
        this.unreadCount = 0;
        showSuccess('已全部标记为已读');
        
      } catch (error) {
        showError(error.message || '操作失败');
      } finally {
        hideLoading();
      }
    },
    
    // 处理消息操作
    handleAction(notification) {
      if (!notification.action_url) return;
      
      // 根据action_url跳转到相应页面
      if (notification.action_url.startsWith('/pages/')) {
        uni.navigateTo({
          url: notification.action_url
        });
      } else if (notification.action_url.startsWith('tab:')) {
        const tabUrl = notification.action_url.replace('tab:', '');
        uni.switchTab({
          url: tabUrl
        });
      }
    },
    
    // 获取消息图标
    getNotificationIcon(type) {
      const iconMap = {
        'order': '📋',
        'product': '📦',
        'system': '⚙️',
        'payment': '💰',
        'promotion': '🎉'
      };
      return iconMap[type] || '📢';
    },
    
    // 获取额外信息文本
    getExtraText(notification) {
      if (!notification.extra_data) return '';
      
      try {
        const extra = JSON.parse(notification.extra_data);
        if (extra.order_number) {
          return `订单号：${extra.order_number}`;
        }
        if (extra.product_name) {
          return `产品：${extra.product_name}`;
        }
        return '';
      } catch (error) {
        return '';
      }
    },
    
    // 工具方法
    getRelativeTime
  }
};
</script>

<style scoped>
.notifications-container {
  background: #f8f8f8;
  min-height: 100vh;
}

.header-actions {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  margin-bottom: 20rpx;
}

.tab-item {
  position: relative;
  padding: 20rpx 30rpx;
  margin-right: 40rpx;
  white-space: nowrap;
}

.tab-item.active .tab-text {
  color: #007AFF;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #007AFF;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #333;
}

.tab-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: #FF3B30;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

.action-buttons {
  text-align: right;
}

.mark-all-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.notification-list {
  padding: 20rpx 30rpx;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 100rpx;
}

.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #666;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.notification-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  border-left: 6rpx solid #007AFF;
}

.notification-icon {
  position: relative;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.icon-text {
  font-size: 40rpx;
}

.unread-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 12rpx;
  height: 12rpx;
  background: #FF3B30;
  border-radius: 6rpx;
}

.notification-content {
  flex: 1;
  margin-right: 20rpx;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.notification-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.notification-time {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
}

.notification-body {
  margin-bottom: 12rpx;
}

.notification-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.notification-extra {
  margin-bottom: 12rpx;
}

.extra-text {
  font-size: 24rpx;
  color: #007AFF;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex-shrink: 0;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  border: none;
  white-space: nowrap;
}

.action-btn.read {
  background: #8E8E93;
  color: white;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-text {
  font-size: 24rpx;
  color: #999;
}
</style>
