#!/bin/bash

# 校园餐智慧食堂API服务启动脚本

echo "🚀 校园餐智慧食堂API服务启动脚本"
echo "=================================="

# 检查Node.js版本
echo "📋 检查环境..."
node_version=$(node -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ Node.js版本: $node_version"
else
    echo "❌ 未安装Node.js，请先安装Node.js 18.x或更高版本"
    exit 1
fi

# 检查npm
npm_version=$(npm -v 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ npm版本: $npm_version"
else
    echo "❌ npm未找到"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 请在backend目录下运行此脚本"
    exit 1
fi

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "❌ 未找到.env配置文件，请先创建.env文件"
    exit 1
fi

echo "✅ 环境检查完成"

# 安装依赖
echo ""
echo "📦 安装依赖包..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 创建上传目录
echo ""
echo "📁 创建上传目录..."
mkdir -p uploads/avatars
mkdir -p uploads/products
echo "✅ 上传目录创建完成"

# 测试数据库连接
echo ""
echo "🔗 测试数据库连接..."
node -e "
const { testConnection } = require('./config/database');
testConnection().then(success => {
    if (success) {
        console.log('✅ 数据库连接成功');
        process.exit(0);
    } else {
        console.log('❌ 数据库连接失败');
        process.exit(1);
    }
}).catch(err => {
    console.log('❌ 数据库连接测试失败:', err.message);
    process.exit(1);
});
"

if [ $? -ne 0 ]; then
    echo "❌ 数据库连接失败，请检查配置"
    echo "💡 提示："
    echo "   1. 检查网络连接"
    echo "   2. 确认数据库服务正在运行"
    echo "   3. 验证.env文件中的数据库配置"
    exit 1
fi

# 询问是否初始化数据库
echo ""
read -p "🗄️  是否需要初始化数据库表？(y/N): " init_db

if [[ $init_db =~ ^[Yy]$ ]]; then
    echo "📊 初始化数据库..."
    
    # 检查mysql命令是否可用
    if command -v mysql &> /dev/null; then
        mysql -h ************** -u StudentsCMSSP -pXg2LS44Cyz5Zt8. StudentsCMSSP < database/init.sql
        
        if [ $? -eq 0 ]; then
            echo "✅ 数据库初始化完成"
        else
            echo "❌ 数据库初始化失败"
            echo "💡 请手动执行 database/init.sql 文件中的SQL语句"
        fi
    else
        echo "⚠️  未找到mysql命令，请手动执行以下命令："
        echo "   mysql -h ************** -u StudentsCMSSP -p StudentsCMSSP < database/init.sql"
    fi
fi

# 选择启动模式
echo ""
echo "🎯 选择启动模式："
echo "1. 开发模式 (推荐，支持热重载)"
echo "2. 生产模式"
read -p "请选择 (1/2): " mode

case $mode in
    1)
        echo ""
        echo "🔥 启动开发模式..."
        echo "📍 服务地址: http://localhost:3000"
        echo "🔄 支持热重载，修改代码后自动重启"
        echo "⏹️  按 Ctrl+C 停止服务"
        echo ""
        npm run dev
        ;;
    2)
        echo ""
        echo "🚀 启动生产模式..."
        echo "📍 服务地址: http://localhost:3000"
        echo "⏹️  按 Ctrl+C 停止服务"
        echo ""
        npm start
        ;;
    *)
        echo "❌ 无效选择，默认启动开发模式"
        echo ""
        npm run dev
        ;;
esac
