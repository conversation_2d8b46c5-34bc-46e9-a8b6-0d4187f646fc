# 微信小程序开发实操指南 - 学校食堂供应商模块

## 开发平台与技术栈

本项目采用以下技术栈进行开发：

1. **微信小程序平台**：作为供应商模块的前端界面
   - 下载地址：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
   - 支持Windows和Mac系统

2. **开发框架**：uni-app（基于Vue）
   - 理由：与现有Flask+Bootstrap技术栈更兼容，学习曲线平缓
   - 开发工具：HBuilderX IDE

3. **后端系统**：
   - 现有Flask Web框架
   - SQL Server数据库
   - RESTful API设计
   - JWT安全认证

4. **微信小程序账号**：个人类型小程序账号
   - 注册地址：https://mp.weixin.qq.com/

## 开发流程详解

### 1. 准备工作

#### 1.1 注册微信小程序账号
1. 访问微信公众平台 https://mp.weixin.qq.com/
2. 点击"立即注册"，选择"小程序"，选择"个人"类型
3. 填写个人信息，完成注册
4. 获取AppID（小程序唯一标识）

> **注意**：个人类型小程序有一定功能限制，例如：
> - 不支持微信支付
> - 部分服务类目受限
> - 每个个人用户最多可注册5个小程序
> - 需要使用中国大陆手机号验证

#### 1.2 安装开发环境
1. 安装Node.js（v14.0以上版本）
2. 安装微信开发者工具
3. 下载安装HBuilderX IDE：https://www.dcloud.io/hbuilderx.html

### 2. 项目创建与配置

#### 2.1 使用uni-app创建项目
1. 打开HBuilderX
2. 点击"文件" > "新建" > "项目"
3. 选择"uni-app"模板
4. 填写项目名称"supplier-miniapp"
5. 选择Vue框架和默认模板

#### 2.2 项目配置
1. 配置小程序基本信息（pages.json和manifest.json）
```json
// pages.json 示例
{
  "pages": [
    {
      "path": "pages/login/index",
      "style": { "navigationBarTitleText": "供应商登录" }
    },
    {
      "path": "pages/dashboard/index",
      "style": { "navigationBarTitleText": "供应商中心" }
    },
    {
      "path": "pages/orders/index",
      "style": { "navigationBarTitleText": "订单管理" }
    },
    {
      "path": "pages/orders/detail",
      "style": { "navigationBarTitleText": "订单详情" }
    },
    {
      "path": "pages/products/index",
      "style": { "navigationBarTitleText": "产品管理" }
    },
    {
      "path": "pages/products/detail",
      "style": { "navigationBarTitleText": "产品详情" }
    },
    {
      "path": "pages/schools/index",
      "style": { "navigationBarTitleText": "合作学校" }
    },
    {
      "path": "pages/profile/index",
      "style": { "navigationBarTitleText": "个人中心" }
    }
  ],
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "list": [
      {
        "pagePath": "pages/dashboard/index",
        "text": "首页",
        "iconPath": "static/images/home.png",
        "selectedIconPath": "static/images/home_active.png"
      },
      {
        "pagePath": "pages/orders/index",
        "text": "订单",
        "iconPath": "static/images/order.png",
        "selectedIconPath": "static/images/order_active.png"
      },
      {
        "pagePath": "pages/products/index",
        "text": "产品",
        "iconPath": "static/images/product.png",
        "selectedIconPath": "static/images/product_active.png"
      },
      {
        "pagePath": "pages/profile/index",
        "text": "我的",
        "iconPath": "static/images/profile.png",
        "selectedIconPath": "static/images/profile_active.png"
      }
    ]
  }
}
```

2. 配置网络请求域名白名单
   - 在微信公众平台 > 开发 > 开发设置 > 服务器域名中添加后端API域名
   - 个人类型小程序每月可修改域名白名单3次

### 3. 数据库模型与API设计

#### 3.1 核心数据库表结构

根据项目数据库设计，供应商小程序主要涉及以下数据表：

1. **供应商表 (suppliers)**
   - id: 主键
   - name: 供应商名称
   - contact_person: 联系人
   - phone: 联系电话
   - email: 电子邮箱
   - address: 地址
   - business_license: 营业执照
   - tax_id: 税号
   - bank_name: 开户银行
   - bank_account: 银行账号
   - status: 状态(0-停用, 1-合作中)
   - rating: 信用等级
   - category_id: 供应商分类ID

2. **供应商分类表 (supplier_categories)**
   - id: 主键
   - name: 分类名称
   - description: 说明

3. **供应商-学校关联表 (supplier_school_relations)**
   - id: 主键
   - supplier_id: 供应商ID
   - area_id: 学校区域ID
   - contract_number: 合同编号
   - start_date: 合作开始日期
   - end_date: 合作结束日期
   - status: 状态(0-已终止, 1-有效)

4. **供应商品表 (supplier_products)**
   - id: 主键
   - supplier_id: 供应商ID
   - ingredient_id: 食材ID
   - product_code: 产品编码
   - product_name: 产品名称
   - specification: 规格
   - price: 单价
   - quality_cert: 质量认证
   - product_image: 产品图片
   - is_available: 是否上架(0-未上架, 1-已上架)
   - shelf_status: 上架状态(0-待审核, 1-已审核, 2-已拒绝)

5. **采购订单项表 (purchase_order_items)**
   - id: 主键
   - order_id: 采购订单ID
   - product_id: 供应商品ID
   - ingredient_id: 食材ID
   - quantity: 数量
   - unit: 单位
   - unit_price: 单价
   - total_price: 总价
   - received_quantity: 已收数量

6. **库存表 (inventories)**
   - id: 主键
   - warehouse_id: 仓库ID
   - storage_location_id: 存储位置ID
   - ingredient_id: 食材ID
   - batch_number: 批次号
   - quantity: 数量
   - unit: 单位
   - production_date: 生产日期
   - expiry_date: 过期日期
   - supplier_id: 供应商ID
   - status: 状态(正常/待检/冻结/已过期)

7. **入库记录表 (stock_ins)**
   - id: 主键
   - stock_in_number: 入库单号
   - warehouse_id: 仓库ID
   - delivery_id: 送货单ID
   - purchase_order_id: 采购订单ID
   - stock_in_date: 入库日期
   - stock_in_type: 入库类型
   - supplier_id: 供应商ID

8. **出库记录表 (stock_outs)**
   - id: 主键
   - stock_out_number: 出库单号
   - warehouse_id: 仓库ID
   - stock_out_date: 出库日期
   - stock_out_type: 出库类型

9. **用户表 (users)**
   - id: 主键
   - username: 用户名
   - password_hash: 密码哈希
   - email: 邮箱
   - real_name: 真实姓名
   - phone: 手机号
   - avatar: 头像
   - last_login: 最后登录时间
   - status: 状态(0-禁用, 1-启用)
   - area_id: 区域ID
   - area_level: 区域级别
   - created_at: 创建时间

10. **角色表 (roles)**
    - id: 主键
    - name: 角色名
    - description: 说明
    - permissions: 权限JSON
    - created_at: 创建时间

#### 3.2 后端API设计

基于上述数据库表结构，设计以下API端点：

1. **认证相关API**
```python
# 供应商登录
@supplier_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    supplier = Supplier.query.filter_by(username=data.get('username')).first()
    if supplier and supplier.check_password(data.get('password')):
        access_token = create_access_token(identity=supplier.id)
        return jsonify(access_token=access_token), 200
    return jsonify(message="Invalid credentials"), 401

# 微信登录并绑定供应商账号
@supplier_bp.route('/wx_login', methods=['POST'])
def wx_login():
    data = request.get_json()
    code = data.get('code')
    # 调用微信API获取openid
    # 查找是否已绑定供应商账号
    # 如未绑定，返回需要绑定的标识
    # 如已绑定，生成token并返回
    pass

# 绑定微信账号
@supplier_bp.route('/bind_account', methods=['POST'])
def bind_account():
    data = request.get_json()
    openid = data.get('openid')
    username = data.get('username')
    password = data.get('password')
    # 验证供应商账号密码
    # 绑定openid到供应商账号
    pass
```

2. **供应商信息API**
```python
# 获取供应商基本信息
@supplier_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    supplier_id = get_jwt_identity()
    supplier = Supplier.query.get(supplier_id)
    return jsonify({
        'id': supplier.id,
        'name': supplier.name,
        'contact_person': supplier.contact_person,
        'phone': supplier.phone,
        'email': supplier.email,
        'address': supplier.address,
        'status': supplier.status,
        'rating': supplier.rating
    })

# 更新供应商联系信息
@supplier_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    supplier_id = get_jwt_identity()
    data = request.get_json()
    supplier = Supplier.query.get(supplier_id)
    # 更新允许修改的字段
    pass
```

3. **产品管理API**
```python
# 获取供应商产品列表
@supplier_bp.route('/products', methods=['GET'])
@jwt_required()
def get_products():
    supplier_id = get_jwt_identity()
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    products = SupplierProduct.query.filter_by(supplier_id=supplier_id).paginate(page=page, per_page=per_page)
    
    result = []
    for product in products.items:
        result.append({
            'id': product.id,
            'product_code': product.product_code,
            'product_name': product.product_name,
            'specification': product.specification,
            'price': float(product.price),
            'is_available': product.is_available,
            'shelf_status': product.shelf_status,
            'product_image': product.product_image
        })
    
    return jsonify({
        'items': result,
        'total': products.total,
        'pages': products.pages,
        'current_page': products.page
    })

# 获取产品详情
@supplier_bp.route('/products/<int:id>', methods=['GET'])
@jwt_required()
def get_product(id):
    supplier_id = get_jwt_identity()
    product = SupplierProduct.query.filter_by(id=id, supplier_id=supplier_id).first_or_404()
    # 返回产品详细信息
    pass

# 更新产品信息
@supplier_bp.route('/products/<int:id>', methods=['PUT'])
@jwt_required()
def update_product(id):
    supplier_id = get_jwt_identity()
    product = SupplierProduct.query.filter_by(id=id, supplier_id=supplier_id).first_or_404()
    data = request.get_json()
    # 更新产品信息
    pass

# 上传产品图片
@supplier_bp.route('/products/<int:id>/image', methods=['POST'])
@jwt_required()
def upload_product_image(id):
    # 处理图片上传
    pass
```

4. **订单管理API**
```python
# 获取订单列表
@supplier_bp.route('/orders', methods=['GET'])
@jwt_required()
def get_orders():
    supplier_id = get_jwt_identity()
    status = request.args.get('status', type=int)
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    # 构建查询
    query = PurchaseOrder.query.filter_by(supplier_id=supplier_id)
    if status:
        query = query.filter_by(status=status)
    
    orders = query.order_by(PurchaseOrder.created_at.desc()).paginate(page=page, per_page=per_page)
    
    # 格式化返回数据
    pass

# 获取订单详情
@supplier_bp.route('/orders/<int:id>', methods=['GET'])
@jwt_required()
def get_order(id):
    supplier_id = get_jwt_identity()
    order = PurchaseOrder.query.filter_by(id=id, supplier_id=supplier_id).first_or_404()
    
    # 获取订单项
    items = []
    for item in order.items:
        items.append({
            'id': item.id,
            'product_name': item.product.product_name,
            'specification': item.product.specification,
            'quantity': item.quantity,
            'unit': item.unit,
            'unit_price': float(item.unit_price),
            'total_price': float(item.total_price)
        })
    
    # 返回订单详情和订单项
    pass

# 接受订单
@supplier_bp.route('/orders/<int:id>/accept', methods=['POST'])
@jwt_required()
def accept_order(id):
    supplier_id = get_jwt_identity()
    order = PurchaseOrder.query.filter_by(id=id, supplier_id=supplier_id).first_or_404()
    
    # 检查订单状态是否为待接受
    if order.status != 1:  # 假设1表示待接受状态
        return jsonify(message="订单状态不允许此操作"), 400
    
    # 更新订单状态为已接受
    order.status = 2  # 假设2表示已接受状态
    db.session.commit()
    
    return jsonify(message="订单已接受"), 200

# 拒绝订单
@supplier_bp.route('/orders/<int:id>/reject', methods=['POST'])
@jwt_required()
def reject_order(id):
    # 类似接受订单的逻辑
    pass

# 标记订单为配送中
@supplier_bp.route('/orders/<int:id>/ship', methods=['POST'])
@jwt_required()
def ship_order(id):
    # 更新订单状态为配送中
    pass
```

5. **学校关系API**
```python
# 获取合作学校列表
@supplier_bp.route('/schools', methods=['GET'])
@jwt_required()
def get_schools():
    supplier_id = get_jwt_identity()
    relations = SupplierSchoolRelation.query.filter_by(
        supplier_id=supplier_id, 
        status=1  # 有效的合作关系
    ).all()
    
    schools = []
    for relation in relations:
        area = relation.area
        schools.append({
            'id': area.id,
            'name': area.name,
            'address': area.address,
            'contract_number': relation.contract_number,
            'start_date': relation.start_date.strftime('%Y-%m-%d'),
            'end_date': relation.end_date.strftime('%Y-%m-%d')
        })
    
    return jsonify(schools)
```

6. **库存和入库API**
```python
# 获取入库记录列表
@supplier_bp.route('/stock_ins', methods=['GET'])
@jwt_required()
def get_stock_ins():
    supplier_id = get_jwt_identity()
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    stock_ins = StockIn.query.filter_by(supplier_id=supplier_id).paginate(page=page, per_page=per_page)
    
    # 格式化返回数据
    pass

# 获取入库记录详情
@supplier_bp.route('/stock_ins/<int:id>', methods=['GET'])
@jwt_required()
def get_stock_in(id):
    supplier_id = get_jwt_identity()
    stock_in = StockIn.query.filter_by(id=id, supplier_id=supplier_id).first_or_404()
    
    # 获取入库明细
    items = []
    for item in stock_in.items:
        items.append({
            'id': item.id,
            'ingredient_name': item.ingredient.name,
            'batch_number': item.batch_number,
            'quantity': item.quantity,
            'unit': item.unit,
            'production_date': item.production_date.strftime('%Y-%m-%d'),
            'expiry_date': item.expiry_date.strftime('%Y-%m-%d'),
            'quality_check_result': item.quality_check_result
        })
    
    # 返回入库记录详情和明细
    pass
```

### 4. 小程序前端开发

#### 4.1 安装必要的依赖
```bash
# 在HBuilderX中打开终端，进入项目目录
npm init -y
npm install uview-ui
npm install vuex
```

#### 4.2 集成uView UI组件库
1. 在main.js中引入uView
```javascript
import uView from 'uview-ui'
Vue.use(uView)
```

2. 在uni.scss中引入样式
```scss
@import 'uview-ui/theme.scss';
```

#### 4.3 实现API请求模块
```javascript
// api/request.js
const BASE_URL = 'https://your-backend-domain.com/api';

// 请求拦截器
const httpInterceptor = {
  invoke(options) {
    options.url = BASE_URL + options.url;
    options.header = {
      ...options.header,
      'Content-Type': 'application/json'
    };
    
    // 添加token
    const token = uni.getStorageSync('token');
    if (token) {
      options.header.Authorization = `Bearer ${token}`;
    }
  }
};

uni.addInterceptor('request', httpInterceptor);
uni.addInterceptor('uploadFile', httpInterceptor);

// 封装请求方法
export const request = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // token过期，跳转登录页
          uni.navigateTo({ url: '/pages/login/index' });
          reject(new Error('未授权或授权已过期'));
        } else {
          reject(new Error(res.data.message || '请求失败'));
        }
      },
      fail: (err) => {
        reject(new Error('网络请求失败'));
      }
    });
  });
};
```

#### 4.4 实现登录页面
```vue
<!-- pages/login/index.vue -->
<template>
  <view class="login-container">
    <u-form :model="form" ref="uForm">
      <u-form-item label="用户名" prop="username">
        <u-input v-model="form.username" placeholder="请输入供应商账号" />
      </u-form-item>
      <u-form-item label="密码" prop="password">
        <u-input v-model="form.password" type="password" placeholder="请输入密码" />
      </u-form-item>
    </u-form>
    <u-button type="primary" @click="handleLogin">登录</u-button>
  </view>
</template>

<script>
import { request } from '@/api/request.js';

export default {
  data() {
    return {
      form: {
        username: '',
        password: ''
      }
    };
  },
  methods: {
    async handleLogin() {
      try {
        const res = await request({
          url: '/supplier/login',
          method: 'POST',
          data: this.form
        });
        
        // 保存token
        uni.setStorageSync('token', res.access_token);
        
        // 跳转到首页
        uni.switchTab({
          url: '/pages/dashboard/index'
        });
      } catch (error) {
        uni.showToast({
          title: error.message,
          icon: 'none'
        });
      }
    }
  }
};
</script>
```

#### 4.5 实现订单管理页面
```vue
<!-- pages/orders/index.vue -->
<template>
  <view class="orders-container">
    <u-tabs :list="tabList" @change="handleTabChange"></u-tabs>
    
    <view class="order-list">
      <view v-for="(order, index) in orderList" :key="index" class="order-item">
        <u-card :title="'订单号: ' + order.order_number" :sub-title="order.create_time">
          <view slot="body">
            <view>学校: {{order.school_name}}</view>
            <view>状态: {{getStatusText(order.status)}}</view>
            <view>总金额: ¥{{order.total_amount}}</view>
          </view>
          <view slot="foot">
            <u-button size="mini" type="primary" @click="viewOrderDetail(order.id)">查看详情</u-button>
            <u-button size="mini" type="success" v-if="order.status === 1" @click="acceptOrder(order.id)">接受订单</u-button>
          </view>
        </u-card>
      </view>
    </view>
    
    <u-loadmore :status="loadMoreStatus" />
  </view>
</template>

<script>
import { request } from '@/api/request.js';

export default {
  data() {
    return {
      tabList: [
        { name: '全部' },
        { name: '待处理' },
        { name: '已接受' },
        { name: '已完成' }
      ],
      currentTab: 0,
      orderList: [],
      page: 1,
      loadMoreStatus: 'loading'
    };
  },
  onLoad() {
    this.getOrderList();
  },
  methods: {
    async getOrderList() {
      try {
        const res = await request({
          url: '/supplier/orders',
          method: 'GET',
          data: {
            status: this.currentTab === 0 ? '' : this.currentTab,
            page: this.page
          }
        });
        
        if (this.page === 1) {
          this.orderList = res.data;
        } else {
          this.orderList = [...this.orderList, ...res.data];
        }
        
        this.loadMoreStatus = res.data.length < 10 ? 'nomore' : 'loadmore';
      } catch (error) {
        uni.showToast({
          title: error.message,
          icon: 'none'
        });
      }
    },
    handleTabChange(index) {
      this.currentTab = index;
      this.page = 1;
      this.getOrderList();
    },
    getStatusText(status) {
      const statusMap = {
        1: '待处理',
        2: '已接受',
        3: '配送中',
        4: '已完成',
        5: '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    viewOrderDetail(id) {
      uni.navigateTo({
        url: `/pages/orders/detail?id=${id}`
      });
    },
    async acceptOrder(id) {
      try {
        await request({
          url: `/supplier/orders/${id}/accept`,
          method: 'POST'
        });
        
        uni.showToast({
          title: '订单已接受',
          icon: 'success'
        });
        
        // 刷新订单列表
        this.page = 1;
        this.getOrderList();
      } catch (error) {
        uni.showToast({
          title: error.message,
          icon: 'none'
        });
      }
    }
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.page = 1;
    this.getOrderList().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  // 上拉加载更多
  onReachBottom() {
    if (this.loadMoreStatus === 'loadmore') {
      this.page++;
      this.getOrderList();
    }
  }
};
</script>
```

### 5. 测试与调试

#### 5.1 本地调试
1. 在HBuilderX中点击"运行">"运行到小程序模拟器">"微信开发者工具"
2. 填入AppID
3. 选择测试环境
4. 进行功能测试和调试

#### 5.2 真机测试
1. 在HBuilderX中点击"发行">"小程序-微信"
2. 生成二维码，手机微信扫码进行真机测试
3. 测试各种设备和网络环境下的表现

### 6. 部署与发布

#### 6.1 代码上传
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传代码到微信服务器

#### 6.2 提交审核
1. 登录微信公众平台
2. 进入"版本管理"
3. 选择已上传的版本提交审核
4. 填写审核相关信息

#### 6.3 发布上线
1. 审核通过后，在微信公众平台点击"发布"
2. 可选择全量发布或分阶段发布
3. 发布后用户可通过搜索或扫码使用小程序

## 与现有系统集成的关键点

### 1. 数据同步策略
1. 使用WebSocket实现订单实时通知
2. 定时轮询获取最新数据
3. 使用消息推送通知供应商新订单

### 2. 认证与安全
1. 使用JWT进行身份验证，与现有系统保持一致
2. 实现微信登录与供应商账号的绑定
3. 敏感操作增加二次确认

### 3. 数据缓存策略
1. 使用本地存储缓存不常变化的数据
2. 实现数据定期更新机制
3. 关键数据实时请求，确保准确性

## 常见问题与解决方案

### 1. 登录认证问题
**问题**：微信登录后如何关联供应商账号？
**解决**：首次登录时，让用户输入供应商账号密码，后端将微信openid与供应商ID关联存储。

### 2. 网络请求问题
**问题**：小程序域名白名单限制
**解决**：在微信公众平台配置业务域名和请求域名白名单，确保包含现有系统的API域名。

### 3. 数据一致性问题
**问题**：小程序与Web系统数据不同步
**解决**：实现数据版本控制，定期检查更新，关键操作实时同步。

### 4. 审核被拒问题
**问题**：小程序提交审核被拒
**解决**：严格遵循微信小程序规范，避免敏感内容，完善隐私声明和用户协议。

## 资源与文档

1. **微信小程序官方文档**：
   https://developers.weixin.qq.com/miniprogram/dev/framework/

2. **uni-app框架文档**：
   https://uniapp.dcloud.io/

3. **uView UI组件库**：
   https://www.uviewui.com/

4. **Flask官方文档**：
   https://flask.palletsprojects.com/

5. **JWT认证文档**：
   https://flask-jwt-extended.readthedocs.io/


