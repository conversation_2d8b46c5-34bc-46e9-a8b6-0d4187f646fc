<template>
  <view class="schools-container">
    <!-- 头部统计 -->
    <view class="header-stats">
      <view class="stat-item">
        <text class="stat-number">{{ schoolList.length }}</text>
        <text class="stat-label">合作学校</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ activeContracts }}</text>
        <text class="stat-label">有效合同</text>
      </view>
    </view>
    
    <!-- 学校列表 -->
    <view class="school-list">
      <view v-if="loading" class="loading-state">
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="schoolList.length === 0" class="empty-state">
        <text class="empty-icon">🏫</text>
        <text class="empty-text">暂无合作学校</text>
      </view>
      
      <view v-else>
        <view 
          v-for="(school, index) in schoolList" 
          :key="index" 
          class="school-item"
          @click="viewSchoolDetail(school.id)"
        >
          <!-- 学校基本信息 -->
          <view class="school-header">
            <view class="school-info">
              <text class="school-name">{{ school.name }}</text>
              <text class="school-address">{{ school.address }}</text>
            </view>
            <view class="contract-status" :class="getContractStatusClass(school)">
              <text class="status-text">{{ getContractStatusText(school) }}</text>
            </view>
          </view>
          
          <!-- 合同信息 -->
          <view class="contract-info">
            <view class="contract-item">
              <text class="contract-label">合同编号：</text>
              <text class="contract-value">{{ school.contract_number }}</text>
            </view>
            <view class="contract-item">
              <text class="contract-label">合作期限：</text>
              <text class="contract-value">
                {{ formatDate(school.start_date) }} 至 {{ formatDate(school.end_date) }}
              </text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="school-actions">
            <button 
              class="action-btn primary"
              @click.stop="viewSchoolOrders(school.id)"
            >
              查看订单
            </button>
            <button 
              class="action-btn secondary"
              @click.stop="contactSchool(school)"
            >
              联系学校
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { schoolAPI } from '@/api/supplier.js';
import { formatDate } from '@/utils/common.js';
import { showError, showLoading, hideLoading } from '@/utils/request.js';

export default {
  data() {
    return {
      schoolList: [],
      loading: false
    };
  },
  
  computed: {
    // 有效合同数量
    activeContracts() {
      return this.schoolList.filter(school => {
        const now = new Date();
        const endDate = new Date(school.end_date);
        return school.status === 1 && endDate > now;
      }).length;
    }
  },
  
  onLoad() {
    this.loadSchoolList();
  },
  
  onPullDownRefresh() {
    this.loadSchoolList().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  
  methods: {
    // 加载学校列表
    async loadSchoolList() {
      this.loading = true;
      showLoading('加载中...');
      
      try {
        const response = await schoolAPI.getSchools();
        this.schoolList = response || [];
        
      } catch (error) {
        showError(error.message || '加载失败');
      } finally {
        this.loading = false;
        hideLoading();
      }
    },
    
    // 获取合同状态样式类
    getContractStatusClass(school) {
      const now = new Date();
      const startDate = new Date(school.start_date);
      const endDate = new Date(school.end_date);
      
      if (school.status !== 1) {
        return 'status-terminated';
      } else if (now < startDate) {
        return 'status-pending';
      } else if (now > endDate) {
        return 'status-expired';
      } else {
        // 检查是否即将到期（30天内）
        const daysToExpire = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
        if (daysToExpire <= 30) {
          return 'status-expiring';
        }
        return 'status-active';
      }
    },
    
    // 获取合同状态文本
    getContractStatusText(school) {
      const now = new Date();
      const startDate = new Date(school.start_date);
      const endDate = new Date(school.end_date);
      
      if (school.status !== 1) {
        return '已终止';
      } else if (now < startDate) {
        return '未开始';
      } else if (now > endDate) {
        return '已过期';
      } else {
        // 检查是否即将到期（30天内）
        const daysToExpire = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
        if (daysToExpire <= 30) {
          return `${daysToExpire}天后到期`;
        }
        return '合作中';
      }
    },
    
    // 查看学校详情
    viewSchoolDetail(id) {
      uni.navigateTo({
        url: `/pages/schools/detail?id=${id}`
      });
    },
    
    // 查看学校订单
    viewSchoolOrders(id) {
      uni.navigateTo({
        url: `/pages/orders/index?school_id=${id}`
      });
    },
    
    // 联系学校
    contactSchool(school) {
      if (!school.contact_phone) {
        showError('该学校未设置联系电话');
        return;
      }
      
      uni.showActionSheet({
        itemList: ['拨打电话', '发送短信'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拨打电话
            uni.makePhoneCall({
              phoneNumber: school.contact_phone,
              fail: (err) => {
                showError('拨打电话失败');
              }
            });
          } else if (res.tapIndex === 1) {
            // 发送短信
            uni.navigateTo({
              url: `/pages/message/send?phone=${school.contact_phone}&name=${school.name}`
            });
          }
        }
      });
    },
    
    // 工具方法
    formatDate
  }
};
</script>

<style scoped>
.schools-container {
  background: #f8f8f8;
  min-height: 100vh;
}

.header-stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.school-list {
  padding: 20rpx 30rpx;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 100rpx;
}

.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #666;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.school-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.school-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.school-info {
  flex: 1;
}

.school-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.school-address {
  font-size: 26rpx;
  color: #666;
}

.contract-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

.status-text {
  font-size: 22rpx;
  color: white;
}

.status-active {
  background: #34C759;
}

.status-pending {
  background: #FF9500;
}

.status-expiring {
  background: #FF6B35;
}

.status-expired {
  background: #8E8E93;
}

.status-terminated {
  background: #FF3B30;
}

.contract-info {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.contract-item {
  display: flex;
  margin-bottom: 12rpx;
}

.contract-item:last-child {
  margin-bottom: 0;
}

.contract-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.contract-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.school-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.secondary {
  background: #34C759;
  color: white;
}
</style>
