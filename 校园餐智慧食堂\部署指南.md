# 校园餐智慧食堂项目部署指南

## 项目概述

本项目包含两个主要部分：
1. **前端小程序** (`校园餐智慧食堂/`) - 微信小程序供应商端
2. **后端API服务** (`校园餐智慧食堂/backend/`) - Node.js API服务

## 🗄️ 数据库信息

**现有数据库配置：**
- **主机**: **************
- **用户名**: StudentsCMSSP  
- **密码**: Xg2LS44Cyz5Zt8.
- **数据库名**: StudentsCMSSP
- **端口**: 3306

## 🚀 快速部署步骤

### 第一步：部署后端API服务

#### 1.1 环境准备
```bash
# 确保已安装Node.js 18.x+
node --version

# 进入后端目录
cd 校园餐智慧食堂/backend
```

#### 1.2 安装依赖
```bash
npm install
```

#### 1.3 配置环境变量
检查并修改 `.env` 文件：
```env
# 数据库配置（已配置好）
DB_HOST=**************
DB_USER=StudentsCMSSP
DB_PASSWORD=Xg2LS44Cyz5Zt8.
DB_NAME=StudentsCMSSP

# 服务器配置
PORT=3000
NODE_ENV=development

# JWT密钥（生产环境请修改）
JWT_SECRET=your_secure_jwt_secret_key_here

# 微信小程序配置（需要配置）
WECHAT_APP_ID=你的微信小程序AppID
WECHAT_APP_SECRET=你的微信小程序AppSecret
```

#### 1.4 初始化数据库
```bash
# 方法1：使用启动脚本（推荐）
./start.sh

# 方法2：手动执行SQL
mysql -h ************** -u StudentsCMSSP -p StudentsCMSSP < database/init.sql
```

#### 1.5 启动API服务
```bash
# 开发模式（推荐）
npm run dev

# 或使用启动脚本
./start.sh
```

服务启动后访问 http://localhost:3000/health 检查状态。

### 第二步：配置微信小程序

#### 2.1 获取微信小程序配置
1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入小程序管理后台
3. 获取 AppID 和 AppSecret

#### 2.2 配置服务器域名
在微信公众平台设置以下域名白名单：
- **request合法域名**: `https://your-api-domain.com`
- **uploadFile合法域名**: `https://your-api-domain.com`
- **downloadFile合法域名**: `https://your-api-domain.com`

#### 2.3 修改小程序配置
编辑 `校园餐智慧食堂/manifest.json`：
```json
{
  "mp-weixin": {
    "appid": "你的微信小程序AppID"
  }
}
```

编辑 `校园餐智慧食堂/utils/request.js`：
```javascript
// 修改API基础地址
const BASE_URL = 'https://your-api-domain.com/api';
// 开发环境可以使用：http://localhost:3000/api
```

#### 2.4 使用HBuilderX运行小程序
1. 用HBuilderX打开 `校园餐智慧食堂` 目录
2. 点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
3. 在微信开发者工具中预览和调试

## 🧪 测试验证

### 测试账号
```
用户名: supplier001
密码: 123456
供应商: 绿色农产品供应商

用户名: supplier002
密码: 123456  
供应商: 优质肉类供应商
```

### API测试
```bash
# 测试登录接口
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"supplier001","password":"123456"}'

# 测试健康检查
curl http://localhost:3000/health
```

### 小程序测试
1. 在微信开发者工具中打开小程序
2. 使用测试账号登录
3. 测试各个功能模块

## 🌐 生产环境部署

### 后端部署

#### 方案1：使用PM2
```bash
# 安装PM2
npm install -g pm2

# 修改环境为生产模式
export NODE_ENV=production

# 启动服务
pm2 start app.js --name "campus-canteen-api"

# 设置开机自启
pm2 startup
pm2 save
```

#### 方案2：使用Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

```bash
# 构建镜像
docker build -t campus-canteen-api .

# 运行容器
docker run -d -p 3000:3000 --name campus-canteen-api campus-canteen-api
```

#### 方案3：使用云服务
推荐使用以下云服务：
- **腾讯云**: 云服务器 + 云数据库
- **阿里云**: ECS + RDS
- **华为云**: 弹性云服务器 + 云数据库

### 小程序发布

#### 1. 代码上传
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传代码到微信服务器

#### 2. 提交审核
1. 登录微信公众平台
2. 进入"版本管理"
3. 选择已上传的版本提交审核

#### 3. 发布上线
1. 审核通过后点击"发布"
2. 选择发布方式
3. 发布后用户可正常使用

## 🔧 配置说明

### 环境变量说明
```env
# 数据库配置
DB_HOST=数据库主机地址
DB_USER=数据库用户名
DB_PASSWORD=数据库密码
DB_NAME=数据库名称
DB_PORT=数据库端口

# 服务器配置
PORT=API服务端口
NODE_ENV=运行环境(development/production)

# JWT配置
JWT_SECRET=JWT密钥(生产环境必须修改)
JWT_EXPIRES_IN=Token过期时间

# 微信配置
WECHAT_APP_ID=微信小程序AppID
WECHAT_APP_SECRET=微信小程序AppSecret

# 文件上传配置
UPLOAD_PATH=文件上传路径
MAX_FILE_SIZE=最大文件大小
ALLOWED_FILE_TYPES=允许的文件类型
```

### 数据库表结构
主要数据表：
- `suppliers` - 供应商表
- `supplier_wechat` - 微信绑定表
- `schools` - 学校表
- `supplier_schools` - 合作关系表
- `products` - 产品表
- `orders` - 订单表
- `order_items` - 订单商品表
- `notifications` - 消息通知表

## 🛠️ 故障排除

### 常见问题

#### 1. 数据库连接失败
**症状**: API启动时提示数据库连接失败
**解决方案**:
- 检查网络连接
- 验证数据库配置信息
- 确认数据库服务正在运行
- 检查防火墙设置

#### 2. 小程序无法登录
**症状**: 小程序登录时提示网络错误
**解决方案**:
- 检查API服务是否正常运行
- 验证request.js中的BASE_URL配置
- 确认微信公众平台的域名白名单设置

#### 3. 图片上传失败
**症状**: 上传图片时失败
**解决方案**:
- 检查uploads目录权限
- 验证文件大小和格式限制
- 确认Sharp图片处理库正常安装

#### 4. JWT Token无效
**症状**: API请求返回认证失败
**解决方案**:
- 检查JWT_SECRET配置
- 验证Token是否过期
- 确认请求头格式正确

### 日志查看
```bash
# PM2日志
pm2 logs campus-canteen-api

# Docker日志
docker logs campus-canteen-api

# 开发模式日志
# 直接在控制台查看
```

## 📞 技术支持

如遇到部署问题，请：
1. 查看错误日志
2. 检查配置文件
3. 参考故障排除指南
4. 联系开发团队

## 📋 检查清单

部署完成后，请确认以下项目：

### 后端API
- [ ] 数据库连接正常
- [ ] API服务启动成功
- [ ] 健康检查接口正常
- [ ] 测试账号可以登录
- [ ] 文件上传功能正常

### 微信小程序
- [ ] AppID配置正确
- [ ] API地址配置正确
- [ ] 域名白名单设置完成
- [ ] 小程序可以正常运行
- [ ] 登录功能正常
- [ ] 各个页面功能正常

### 生产环境
- [ ] HTTPS证书配置
- [ ] 域名解析正确
- [ ] 防火墙规则设置
- [ ] 数据备份策略
- [ ] 监控告警配置

---

**部署完成！** 🎉

您的校园餐智慧食堂供应商端小程序现在已经可以正常使用了。
