<template>
  <view class="help-container">
    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <text class="search-icon">🔍</text>
        <input 
          class="search-input" 
          v-model="searchKeyword" 
          placeholder="搜索帮助内容"
          @input="handleSearch"
        />
        <text v-if="searchKeyword" class="clear-icon" @click="clearSearch">✕</text>
      </view>
    </view>
    
    <!-- 常见问题 -->
    <view class="faq-section">
      <view class="section-title">
        <text class="title-text">常见问题</text>
      </view>
      
      <view class="faq-list">
        <view 
          v-for="(faq, index) in filteredFAQs" 
          :key="index"
          class="faq-item"
          @click="toggleFAQ(index)"
        >
          <view class="faq-question">
            <text class="question-text">{{ faq.question }}</text>
            <text class="expand-icon" :class="{ 'expanded': faq.expanded }">></text>
          </view>
          <view v-if="faq.expanded" class="faq-answer">
            <text class="answer-text">{{ faq.answer }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 功能指南 -->
    <view class="guide-section">
      <view class="section-title">
        <text class="title-text">功能指南</text>
      </view>
      
      <view class="guide-list">
        <view 
          v-for="(guide, index) in guides" 
          :key="index"
          class="guide-item"
          @click="viewGuide(guide)"
        >
          <text class="guide-icon">{{ guide.icon }}</text>
          <view class="guide-info">
            <text class="guide-title">{{ guide.title }}</text>
            <text class="guide-desc">{{ guide.description }}</text>
          </view>
          <text class="guide-arrow">></text>
        </view>
      </view>
    </view>
    
    <!-- 联系我们 -->
    <view class="contact-section">
      <view class="section-title">
        <text class="title-text">联系我们</text>
      </view>
      
      <view class="contact-list">
        <view class="contact-item" @click="makePhoneCall">
          <text class="contact-icon">📞</text>
          <view class="contact-info">
            <text class="contact-title">客服电话</text>
            <text class="contact-value">************</text>
          </view>
        </view>
        
        <view class="contact-item" @click="sendEmail">
          <text class="contact-icon">📧</text>
          <view class="contact-info">
            <text class="contact-title">客服邮箱</text>
            <text class="contact-value"><EMAIL></text>
          </view>
        </view>
        
        <view class="contact-item" @click="openFeedback">
          <text class="contact-icon">💬</text>
          <view class="contact-info">
            <text class="contact-title">意见反馈</text>
            <text class="contact-value">提交问题和建议</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 悬浮反馈按钮 -->
    <view class="fab" @click="openFeedback">
      <text class="fab-icon">💬</text>
    </view>
  </view>
</template>

<script>
import { debounce } from '@/utils/common.js';
import { showError } from '@/utils/request.js';

export default {
  data() {
    return {
      searchKeyword: '',
      faqs: [
        {
          question: '如何登录小程序？',
          answer: '您可以使用供应商账号密码登录，或者使用微信快捷登录。首次使用微信登录需要绑定供应商账号。',
          expanded: false
        },
        {
          question: '如何处理新订单？',
          answer: '收到新订单后，请及时查看订单详情，确认商品信息无误后点击"接受订单"。如有问题可以选择"拒绝订单"并说明原因。',
          expanded: false
        },
        {
          question: '如何上架新产品？',
          answer: '在产品管理页面点击"+"按钮添加新产品，填写完整的产品信息并上传图片，提交后等待审核通过即可上架。',
          expanded: false
        },
        {
          question: '订单状态说明',
          answer: '订单状态包括：待处理（需要您确认）、已接受（准备配送）、配送中（正在送货）、已完成（交易完成）、已取消（订单取消）。',
          expanded: false
        },
        {
          question: '如何修改产品价格？',
          answer: '在产品列表中点击要修改的产品，进入产品详情页面，点击"编辑"按钮即可修改价格和其他信息。',
          expanded: false
        },
        {
          question: '忘记密码怎么办？',
          answer: '如果忘记密码，请联系系统管理员重置密码，或者使用微信登录功能。',
          expanded: false
        }
      ],
      guides: [
        {
          title: '订单管理指南',
          description: '学习如何高效处理订单',
          icon: '📋',
          content: 'order_guide'
        },
        {
          title: '产品管理指南',
          description: '了解产品上架和管理流程',
          icon: '📦',
          content: 'product_guide'
        },
        {
          title: '数据统计说明',
          description: '理解各项数据指标含义',
          icon: '📊',
          content: 'data_guide'
        },
        {
          title: '账户安全设置',
          description: '保护您的账户安全',
          icon: '🔒',
          content: 'security_guide'
        }
      ]
    };
  },
  
  computed: {
    filteredFAQs() {
      if (!this.searchKeyword.trim()) {
        return this.faqs;
      }
      
      const keyword = this.searchKeyword.toLowerCase();
      return this.faqs.filter(faq => 
        faq.question.toLowerCase().includes(keyword) || 
        faq.answer.toLowerCase().includes(keyword)
      );
    }
  },
  
  methods: {
    // 搜索处理（防抖）
    handleSearch: debounce(function() {
      // 搜索逻辑已在computed中处理
    }, 300),
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
    },
    
    // 切换FAQ展开状态
    toggleFAQ(index) {
      const faq = this.filteredFAQs[index];
      faq.expanded = !faq.expanded;
    },
    
    // 查看指南
    viewGuide(guide) {
      uni.navigateTo({
        url: `/pages/help/guide?type=${guide.content}&title=${guide.title}`
      });
    },
    
    // 拨打客服电话
    makePhoneCall() {
      uni.makePhoneCall({
        phoneNumber: '************',
        fail: (err) => {
          showError('拨打电话失败');
        }
      });
    },
    
    // 发送邮件
    sendEmail() {
      // 在小程序中无法直接发送邮件，可以复制邮箱地址
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱地址已复制',
            icon: 'success'
          });
        }
      });
    },
    
    // 打开反馈页面
    openFeedback() {
      uni.navigateTo({
        url: '/pages/help/feedback'
      });
    }
  }
};
</script>

<style scoped>
.help-container {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.search-section {
  background: white;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
  margin-left: 10rpx;
}

.faq-section,
.guide-section,
.contact-section {
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx;
  background: #f8f8f8;
}

.title-text {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.faq-list {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.faq-item {
  border-bottom: 2rpx solid #f8f8f8;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
}

.question-text {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  flex: 1;
}

.expand-icon {
  font-size: 28rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.faq-answer {
  padding: 0 30rpx 30rpx;
  background: #f8f8f8;
}

.answer-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.guide-list,
.contact-list {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.guide-item,
.contact-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.guide-item:last-child,
.contact-item:last-child {
  border-bottom: none;
}

.guide-icon,
.contact-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.guide-info,
.contact-info {
  flex: 1;
}

.guide-title,
.contact-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.guide-desc,
.contact-value {
  font-size: 24rpx;
  color: #666;
}

.guide-arrow {
  font-size: 28rpx;
  color: #999;
}

.fab {
  position: fixed;
  bottom: 100rpx;
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background: #007AFF;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.3);
  z-index: 100;
}

.fab-icon {
  font-size: 40rpx;
  color: white;
}
</style>
