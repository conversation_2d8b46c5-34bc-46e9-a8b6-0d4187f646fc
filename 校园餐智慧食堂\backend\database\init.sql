-- 校园餐智慧食堂数据库初始化脚本
-- 数据库：StudentsCMSSP

USE StudentsCMSSP;

-- 1. 供应商表
CREATE TABLE IF NOT EXISTS suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    name VARCHAR(100) NOT NULL COMMENT '供应商名称',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(255) COMMENT '头像URL',
    contact_person VARCHAR(50) COMMENT '联系人',
    address TEXT COMMENT '地址',
    business_license VARCHAR(50) COMMENT '营业执照号',
    tax_id VARCHAR(50) COMMENT '税务登记号',
    bank_name VARCHAR(100) COMMENT '开户银行',
    bank_account VARCHAR(50) COMMENT '银行账号',
    rating CHAR(1) DEFAULT 'A' COMMENT '信用等级',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at DATETIME COMMENT '最后登录时间',
    INDEX idx_username (username),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商表';

-- 2. 供应商微信绑定表
CREATE TABLE IF NOT EXISTS supplier_wechat (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_id INT NOT NULL COMMENT '供应商ID',
    openid VARCHAR(100) NOT NULL UNIQUE COMMENT '微信openid',
    unionid VARCHAR(100) COMMENT '微信unionid',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_openid (openid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商微信绑定表';

-- 3. 学校表
CREATE TABLE IF NOT EXISTS schools (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '学校名称',
    address TEXT COMMENT '学校地址',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    description TEXT COMMENT '学校描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学校表';

-- 4. 供应商学校合作关系表
CREATE TABLE IF NOT EXISTS supplier_schools (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_id INT NOT NULL COMMENT '供应商ID',
    school_id INT NOT NULL COMMENT '学校ID',
    contract_number VARCHAR(50) COMMENT '合同编号',
    start_date DATE COMMENT '合作开始日期',
    end_date DATE COMMENT '合作结束日期',
    status TINYINT DEFAULT 1 COMMENT '状态：0-终止，1-合作中',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE CASCADE,
    UNIQUE KEY uk_supplier_school (supplier_id, school_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_school_id (school_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商学校合作关系表';

-- 5. 产品表
CREATE TABLE IF NOT EXISTS products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_id INT NOT NULL COMMENT '供应商ID',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    product_code VARCHAR(50) NOT NULL COMMENT '产品编码',
    product_image VARCHAR(255) COMMENT '产品图片URL',
    specification VARCHAR(200) COMMENT '规格说明',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    unit VARCHAR(20) DEFAULT '件' COMMENT '单位',
    quality_cert VARCHAR(200) COMMENT '质量认证',
    is_available TINYINT DEFAULT 0 COMMENT '是否上架：0-下架，1-上架',
    shelf_status TINYINT DEFAULT 0 COMMENT '审核状态：0-待审核，1-已通过，2-已拒绝',
    shelf_reason VARCHAR(200) COMMENT '审核备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    UNIQUE KEY uk_supplier_code (supplier_id, product_code),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_product_name (product_name),
    INDEX idx_product_code (product_code),
    INDEX idx_is_available (is_available),
    INDEX idx_shelf_status (shelf_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- 6. 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    supplier_id INT NOT NULL COMMENT '供应商ID',
    school_id INT NOT NULL COMMENT '学校ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    subtotal DECIMAL(10,2) NOT NULL COMMENT '商品小计',
    delivery_fee DECIMAL(10,2) DEFAULT 0 COMMENT '配送费',
    status TINYINT DEFAULT 1 COMMENT '订单状态：1-待处理，2-已接受，3-配送中，4-已完成，5-已取消',
    delivery_address TEXT COMMENT '配送地址',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    notes TEXT COMMENT '订单备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    accepted_at DATETIME COMMENT '接受时间',
    shipped_at DATETIME COMMENT '发货时间',
    completed_at DATETIME COMMENT '完成时间',
    cancelled_at DATETIME COMMENT '取消时间',
    cancel_reason VARCHAR(200) COMMENT '取消原因',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE CASCADE,
    INDEX idx_order_number (order_number),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_school_id (school_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 7. 订单商品表
CREATE TABLE IF NOT EXISTS order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL COMMENT '订单ID',
    product_id INT NOT NULL COMMENT '产品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称（冗余）',
    specification VARCHAR(200) COMMENT '规格说明',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    quantity INT NOT NULL COMMENT '数量',
    unit VARCHAR(20) DEFAULT '件' COMMENT '单位',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';

-- 8. 消息通知表
CREATE TABLE IF NOT EXISTS notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_id INT NOT NULL COMMENT '供应商ID',
    title VARCHAR(100) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type VARCHAR(20) DEFAULT 'system' COMMENT '通知类型：order-订单，product-产品，system-系统，payment-支付',
    action_url VARCHAR(255) COMMENT '操作链接',
    extra_data JSON COMMENT '额外数据',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    read_at DATETIME COMMENT '阅读时间',
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息通知表';

-- 插入测试数据

-- 插入测试供应商（密码：123456）
INSERT INTO suppliers (username, password, name, phone, email, contact_person, address, business_license, status) VALUES
('supplier001', '$2a$12$rQZ8vQZ8vQZ8vQZ8vQZ8vOZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQ', '绿色农产品供应商', '***********', '<EMAIL>', '张经理', '北京市朝阳区农业园区1号', 'BL001234567890', 1),
('supplier002', '$2a$12$rQZ8vQZ8vQZ8vQZ8vQZ8vOZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQ', '优质肉类供应商', '***********', '<EMAIL>', '李经理', '北京市海淀区食品园区2号', 'BL001234567891', 1);

-- 插入测试学校
INSERT INTO schools (name, address, contact_person, contact_phone, contact_email) VALUES
('北京大学', '北京市海淀区颐和园路5号', '王老师', '010-62751234', '<EMAIL>'),
('清华大学', '北京市海淀区清华园1号', '李老师', '010-62781234', '<EMAIL>'),
('北京师范大学', '北京市海淀区新街口外大街19号', '赵老师', '010-58801234', '<EMAIL>');

-- 插入供应商学校合作关系
INSERT INTO supplier_schools (supplier_id, school_id, contract_number, start_date, end_date, status) VALUES
(1, 1, 'HT2024001', '2024-01-01', '2024-12-31', 1),
(1, 2, 'HT2024002', '2024-01-01', '2024-12-31', 1),
(2, 1, 'HT2024003', '2024-01-01', '2024-12-31', 1);

-- 插入测试产品
INSERT INTO products (supplier_id, product_name, product_code, specification, price, unit, quality_cert, is_available, shelf_status) VALUES
(1, '有机白菜', 'VEG001', '500g/袋，有机认证', 8.50, '袋', '有机产品认证证书', 1, 1),
(1, '新鲜胡萝卜', 'VEG002', '1kg/袋，当日采摘', 6.80, '袋', '绿色食品认证', 1, 1),
(1, '精品土豆', 'VEG003', '2kg/袋，沙土种植', 12.00, '袋', '无公害农产品认证', 1, 1),
(2, '优质猪肉', 'MEAT001', '500g/份，冷鲜肉', 28.00, '份', '动物检疫合格证', 1, 1),
(2, '新鲜鸡蛋', 'EGG001', '30枚/盒，当日产', 25.00, '盒', '无抗生素认证', 1, 1);

-- 插入测试订单
INSERT INTO orders (order_number, supplier_id, school_id, total_amount, subtotal, delivery_fee, status, delivery_address, contact_person, contact_phone, notes) VALUES
('ORD202401001', 1, 1, 156.50, 150.00, 6.50, 2, '北京大学第一食堂', '王师傅', '***********', '请在上午10点前送达'),
('ORD202401002', 1, 2, 89.30, 85.00, 4.30, 1, '清华大学第二食堂', '李师傅', '***********', ''),
('ORD202401003', 2, 1, 265.00, 260.00, 5.00, 3, '北京大学第三食堂', '张师傅', '13800138003', '肉类需要冷链配送');

-- 插入订单商品
INSERT INTO order_items (order_id, product_id, product_name, specification, unit_price, quantity, unit, total_price) VALUES
(1, 1, '有机白菜', '500g/袋，有机认证', 8.50, 10, '袋', 85.00),
(1, 2, '新鲜胡萝卜', '1kg/袋，当日采摘', 6.80, 5, '袋', 34.00),
(1, 3, '精品土豆', '2kg/袋，沙土种植', 12.00, 2, '袋', 24.00),
(2, 1, '有机白菜', '500g/袋，有机认证', 8.50, 10, '袋', 85.00),
(3, 4, '优质猪肉', '500g/份，冷鲜肉', 28.00, 8, '份', 224.00),
(3, 5, '新鲜鸡蛋', '30枚/盒，当日产', 25.00, 1, '盒', 25.00);

-- 插入测试通知
INSERT INTO notifications (supplier_id, title, content, type, action_url, is_read) VALUES
(1, '新订单通知', '您有一个新的订单ORD202401002，请及时处理', 'order', '/pages/orders/detail?id=2', 0),
(1, '产品审核通过', '您的产品"有机白菜"已通过审核，可以正常销售', 'product', '/pages/products/detail?id=1', 1),
(2, '订单状态更新', '订单ORD202401003已开始配送', 'order', '/pages/orders/detail?id=3', 0);

COMMIT;
