<template>
  <view class="profile-edit-container">
    <view v-if="loading" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>
    
    <view v-else>
      <!-- 头像编辑 -->
      <view class="avatar-section">
        <view class="avatar-container" @click="changeAvatar">
          <image 
            class="avatar" 
            :src="profileInfo.avatar || '/static/default-avatar.png'" 
            mode="aspectFill"
          ></image>
          <view class="avatar-overlay">
            <text class="camera-icon">📷</text>
            <text class="change-text">更换头像</text>
          </view>
        </view>
        <view v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: uploadProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ uploadProgress }}%</text>
        </view>
      </view>
      
      <!-- 基本信息表单 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">供应商名称</text>
          <input 
            class="form-input" 
            v-model="profileInfo.name" 
            placeholder="请输入供应商名称"
            maxlength="50"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">联系人</text>
          <input 
            class="form-input" 
            v-model="profileInfo.contact_person" 
            placeholder="请输入联系人姓名"
            maxlength="20"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">联系电话</text>
          <input 
            class="form-input" 
            v-model="profileInfo.phone" 
            placeholder="请输入联系电话"
            type="number"
            maxlength="11"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">电子邮箱</text>
          <input 
            class="form-input" 
            v-model="profileInfo.email" 
            placeholder="请输入电子邮箱"
            type="email"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">详细地址</text>
          <textarea 
            class="form-textarea" 
            v-model="profileInfo.address" 
            placeholder="请输入详细地址"
            maxlength="200"
            auto-height
          ></textarea>
        </view>
      </view>
      
      <!-- 企业信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">企业信息</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">营业执照号</text>
          <input 
            class="form-input" 
            v-model="profileInfo.business_license" 
            placeholder="请输入营业执照号"
            maxlength="30"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">税务登记号</text>
          <input 
            class="form-input" 
            v-model="profileInfo.tax_id" 
            placeholder="请输入税务登记号"
            maxlength="30"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">开户银行</text>
          <input 
            class="form-input" 
            v-model="profileInfo.bank_name" 
            placeholder="请输入开户银行"
            maxlength="50"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">银行账号</text>
          <input 
            class="form-input" 
            v-model="profileInfo.bank_account" 
            placeholder="请输入银行账号"
            type="number"
            maxlength="30"
          />
        </view>
      </view>
      
      <!-- 保存按钮 -->
      <view class="action-buttons">
        <button 
          class="save-btn"
          @click="saveProfile"
          :disabled="!canSave || saving"
        >
          {{ saving ? '保存中...' : '保存修改' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { supplierAPI, fileAPI } from '@/api/supplier.js';
import { validatePhone, validateEmail } from '@/utils/common.js';
import { showError, showSuccess, showLoading, hideLoading, compressImage, upload } from '@/utils/request.js';

export default {
  data() {
    return {
      profileInfo: {
        name: '',
        contact_person: '',
        phone: '',
        email: '',
        address: '',
        business_license: '',
        tax_id: '',
        bank_name: '',
        bank_account: '',
        avatar: ''
      },
      originalProfileInfo: {},
      loading: false,
      saving: false,
      uploadProgress: 0
    };
  },
  
  computed: {
    canSave() {
      return this.profileInfo.name.trim() && 
             this.profileInfo.contact_person.trim() && 
             this.profileInfo.phone.trim() &&
             this.hasChanges();
    },
    
    hasChanges() {
      return JSON.stringify(this.profileInfo) !== JSON.stringify(this.originalProfileInfo);
    }
  },
  
  onLoad() {
    this.loadProfileInfo();
  },
  
  methods: {
    // 加载个人信息
    async loadProfileInfo() {
      this.loading = true;
      showLoading('加载中...');
      
      try {
        const response = await supplierAPI.getProfile();
        this.profileInfo = { ...response };
        this.originalProfileInfo = { ...response };
        
      } catch (error) {
        showError(error.message || '加载失败');
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } finally {
        this.loading = false;
        hideLoading();
      }
    },
    
    // 更换头像
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadAvatar(res.tempFilePaths[0]);
        },
        fail: (err) => {
          showError('选择图片失败');
        }
      });
    },
    
    // 上传头像
    async uploadAvatar(filePath) {
      try {
        this.uploadProgress = 0;
        
        // 压缩图片
        const compressedPath = await compressImage(filePath, 0.7);
        
        // 上传图片
        const response = await upload(
          '/supplier/upload/avatar', 
          compressedPath, 
          { type: 'avatar' },
          (progress) => {
            this.uploadProgress = progress;
          }
        );
        
        this.profileInfo.avatar = response.url;
        this.uploadProgress = 100;
        
        showSuccess('头像上传成功');
        
        // 重置进度
        setTimeout(() => {
          this.uploadProgress = 0;
        }, 1000);
        
      } catch (error) {
        this.uploadProgress = 0;
        showError(error.message || '上传失败');
      }
    },
    
    // 保存个人信息
    async saveProfile() {
      if (!this.canSave || this.saving) return;
      
      // 验证手机号
      if (this.profileInfo.phone && !validatePhone(this.profileInfo.phone)) {
        showError('请输入正确的手机号');
        return;
      }
      
      // 验证邮箱
      if (this.profileInfo.email && !validateEmail(this.profileInfo.email)) {
        showError('请输入正确的邮箱地址');
        return;
      }
      
      this.saving = true;
      showLoading('保存中...');
      
      try {
        await supplierAPI.updateProfile(this.profileInfo);
        
        // 更新本地存储
        uni.setStorageSync('userInfo', this.profileInfo);
        
        // 更新原始数据
        this.originalProfileInfo = { ...this.profileInfo };
        
        showSuccess('保存成功');
        
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
        
      } catch (error) {
        showError(error.message || '保存失败');
      } finally {
        this.saving = false;
        hideLoading();
      }
    }
  }
};
</script>

<style scoped>
.profile-edit-container {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.loading-state {
  text-align: center;
  padding: 200rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.avatar-section {
  background: white;
  padding: 40rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #f0f0f0;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  font-size: 20rpx;
}

.camera-icon {
  margin-right: 6rpx;
}

.upload-progress {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  width: 200rpx;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.progress-fill {
  height: 100%;
  background: #007AFF;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #007AFF;
}

.form-section {
  background: white;
  margin-bottom: 20rpx;
  padding: 40rpx 30rpx;
}

.section-title {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #007AFF;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.save-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.save-btn:disabled {
  opacity: 0.6;
}
</style>
