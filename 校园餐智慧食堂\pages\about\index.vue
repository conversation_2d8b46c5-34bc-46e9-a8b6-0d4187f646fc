<template>
  <view class="about-container">
    <!-- 应用信息 -->
    <view class="app-info">
      <image class="app-logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">校园餐智慧食堂</text>
      <text class="app-subtitle">供应商管理平台</text>
      <text class="app-version">版本 v1.0.0</text>
    </view>
    
    <!-- 应用介绍 -->
    <view class="intro-section">
      <view class="section-title">
        <text class="title-text">应用介绍</text>
      </view>
      <view class="intro-content">
        <text class="intro-text">
          校园餐智慧食堂供应商端是专为食堂供应商打造的移动管理平台。通过本应用，供应商可以随时随地管理订单、产品、查看收入统计，与学校食堂保持高效沟通，提升业务管理效率。
        </text>
      </view>
    </view>
    
    <!-- 主要功能 -->
    <view class="features-section">
      <view class="section-title">
        <text class="title-text">主要功能</text>
      </view>
      <view class="features-list">
        <view 
          v-for="(feature, index) in features" 
          :key="index"
          class="feature-item"
        >
          <text class="feature-icon">{{ feature.icon }}</text>
          <view class="feature-info">
            <text class="feature-title">{{ feature.title }}</text>
            <text class="feature-desc">{{ feature.description }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 公司信息 -->
    <view class="company-section">
      <view class="section-title">
        <text class="title-text">公司信息</text>
      </view>
      <view class="company-info">
        <view class="info-item">
          <text class="info-label">公司名称：</text>
          <text class="info-value">智慧餐饮科技有限公司</text>
        </view>
        <view class="info-item">
          <text class="info-label">成立时间：</text>
          <text class="info-value">2020年</text>
        </view>
        <view class="info-item">
          <text class="info-label">公司地址：</text>
          <text class="info-value">北京市朝阳区科技园区</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系电话：</text>
          <text class="info-value phone" @click="makePhoneCall">************</text>
        </view>
        <view class="info-item">
          <text class="info-label">官方网站：</text>
          <text class="info-value link" @click="openWebsite">www.example.com</text>
        </view>
      </view>
    </view>
    
    <!-- 更新日志 -->
    <view class="changelog-section">
      <view class="section-title">
        <text class="title-text">更新日志</text>
      </view>
      <view class="changelog-list">
        <view 
          v-for="(log, index) in changelog" 
          :key="index"
          class="changelog-item"
        >
          <view class="changelog-header">
            <text class="changelog-version">{{ log.version }}</text>
            <text class="changelog-date">{{ log.date }}</text>
          </view>
          <view class="changelog-content">
            <text 
              v-for="(change, changeIndex) in log.changes" 
              :key="changeIndex"
              class="changelog-change"
            >
              • {{ change }}
            </text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 法律信息 -->
    <view class="legal-section">
      <view class="section-title">
        <text class="title-text">法律信息</text>
      </view>
      <view class="legal-links">
        <view class="legal-item" @click="openPrivacyPolicy">
          <text class="legal-text">隐私政策</text>
          <text class="legal-arrow">></text>
        </view>
        <view class="legal-item" @click="openTermsOfService">
          <text class="legal-text">服务条款</text>
          <text class="legal-arrow">></text>
        </view>
        <view class="legal-item" @click="openUserAgreement">
          <text class="legal-text">用户协议</text>
          <text class="legal-arrow">></text>
        </view>
      </view>
    </view>
    
    <!-- 版权信息 -->
    <view class="copyright-section">
      <text class="copyright-text">© 2024 智慧餐饮科技有限公司</text>
      <text class="copyright-text">保留所有权利</text>
    </view>
  </view>
</template>

<script>
import { showError } from '@/utils/request.js';

export default {
  data() {
    return {
      features: [
        {
          icon: '📋',
          title: '订单管理',
          description: '实时查看和处理订单，支持订单状态跟踪'
        },
        {
          icon: '📦',
          title: '产品管理',
          description: '便捷的产品信息管理和上架下架操作'
        },
        {
          icon: '📊',
          title: '数据统计',
          description: '直观的收入统计和业务数据分析'
        },
        {
          icon: '🏫',
          title: '学校合作',
          description: '管理合作学校信息和合同状态'
        },
        {
          icon: '💬',
          title: '消息通知',
          description: '及时接收订单和业务相关通知'
        },
        {
          icon: '⚙️',
          title: '账户设置',
          description: '个人信息管理和系统设置'
        }
      ],
      changelog: [
        {
          version: 'v1.0.0',
          date: '2024-01-01',
          changes: [
            '发布初始版本',
            '实现订单管理功能',
            '实现产品管理功能',
            '实现数据统计功能',
            '实现用户认证系统'
          ]
        }
      ]
    };
  },
  
  methods: {
    // 拨打电话
    makePhoneCall() {
      uni.makePhoneCall({
        phoneNumber: '************',
        fail: (err) => {
          showError('拨打电话失败');
        }
      });
    },
    
    // 打开网站
    openWebsite() {
      uni.setClipboardData({
        data: 'www.example.com',
        success: () => {
          uni.showToast({
            title: '网址已复制',
            icon: 'success'
          });
        }
      });
    },
    
    // 打开隐私政策
    openPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/about/privacy'
      });
    },
    
    // 打开服务条款
    openTermsOfService() {
      uni.navigateTo({
        url: '/pages/about/terms'
      });
    },
    
    // 打开用户协议
    openUserAgreement() {
      uni.navigateTo({
        url: '/pages/about/agreement'
      });
    }
  }
};
</script>

<style scoped>
.about-container {
  background: #f8f8f8;
  min-height: 100vh;
}

.app-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
  text-align: center;
  color: white;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 20rpx;
}

.app-version {
  display: block;
  font-size: 24rpx;
  opacity: 0.6;
}

.intro-section,
.features-section,
.company-section,
.changelog-section,
.legal-section {
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx;
  background: #f8f8f8;
}

.title-text {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.intro-content {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.intro-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
}

.features-list {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.feature-info {
  flex: 1;
}

.feature-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.company-info {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.info-value.phone,
.info-value.link {
  color: #007AFF;
  text-decoration: underline;
}

.changelog-list {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.changelog-item {
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.changelog-item:last-child {
  border-bottom: none;
}

.changelog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.changelog-version {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.changelog-date {
  font-size: 24rpx;
  color: #666;
}

.changelog-content {
  /* 更新内容样式 */
}

.changelog-change {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.changelog-change:last-child {
  margin-bottom: 0;
}

.legal-links {
  background: white;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.legal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #f8f8f8;
}

.legal-item:last-child {
  border-bottom: none;
}

.legal-text {
  font-size: 28rpx;
  color: #333;
}

.legal-arrow {
  font-size: 28rpx;
  color: #999;
}

.copyright-section {
  text-align: center;
  padding: 40rpx;
  background: #f8f8f8;
}

.copyright-text {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.copyright-text:last-child {
  margin-bottom: 0;
}
</style>
