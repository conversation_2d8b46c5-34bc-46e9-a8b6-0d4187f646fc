const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// 导入数据库配置
const { testConnection } = require('./config/database');

// 导入路由
const supplierRoutes = require('./routes/supplier');
const authRoutes = require('./routes/auth');
const orderRoutes = require('./routes/orders');
const productRoutes = require('./routes/products');
const schoolRoutes = require('./routes/schools');
const uploadRoutes = require('./routes/upload');

// 导入中间件
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// 压缩中间件
app.use(compression());

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// 请求日志
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

// 请求体解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 速率限制
const limiter = rateLimit({
  windowMs: (process.env.RATE_LIMIT_WINDOW || 15) * 60 * 1000, // 15分钟
  max: process.env.RATE_LIMIT_MAX || 100, // 限制每个IP 100个请求
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api', limiter);

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API路由
const apiPrefix = process.env.API_PREFIX || '/api';

// 认证相关路由（不需要token验证）
app.use(`${apiPrefix}/auth`, authRoutes);

// 文件上传路由（需要token验证）
app.use(`${apiPrefix}/upload`, authMiddleware, uploadRoutes);

// 供应商相关路由（需要token验证）
app.use(`${apiPrefix}/supplier`, authMiddleware, supplierRoutes);
app.use(`${apiPrefix}/supplier/orders`, authMiddleware, orderRoutes);
app.use(`${apiPrefix}/supplier/products`, authMiddleware, productRoutes);
app.use(`${apiPrefix}/supplier/schools`, authMiddleware, schoolRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    code: 'NOT_FOUND',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 创建上传目录
    const fs = require('fs');
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('📁 创建上传目录:', uploadDir);
    }

    // 启动服务器
    app.listen(PORT, () => {
      console.log('\n🚀 服务器启动成功!');
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🔗 API前缀: ${apiPrefix}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
      console.log('\n📋 可用的API端点:');
      console.log(`   POST ${apiPrefix}/auth/login - 供应商登录`);
      console.log(`   POST ${apiPrefix}/auth/wx_login - 微信登录`);
      console.log(`   GET  ${apiPrefix}/supplier/profile - 获取供应商信息`);
      console.log(`   GET  ${apiPrefix}/supplier/orders - 获取订单列表`);
      console.log(`   GET  ${apiPrefix}/supplier/products - 获取产品列表`);
      console.log(`   GET  /health - 健康检查`);
      console.log('\n✨ 服务器已就绪，等待请求...\n');
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🔄 正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🔄 正在关闭服务器...');
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
startServer();

module.exports = app;
