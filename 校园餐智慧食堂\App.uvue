<script lang="uts">
	let firstBackTime = 0
	export default {
		onLaunch: function () {
			console.log('校园餐智慧食堂供应商端启动')

			// 检查登录状态
			this.checkLoginStatus()
		},
		onShow: function () {
			console.log('App Show')
		},
		onHide: function () {
			console.log('App Hide')
		},
		// #ifdef APP-ANDROID
		onLastPageBackPress: function () {
			console.log('App LastPageBackPress')
			if (firstBackTime == 0) {
				uni.showToast({
					title: '再按一次退出应用',
					position: 'bottom',
				})
				firstBackTime = Date.now()
				setTimeout(() => {
					firstBackTime = 0
				}, 2000)
			} else if (Date.now() - firstBackTime < 2000) {
				firstBackTime = Date.now()
				uni.exit()
			}
		},
		// #endif
		onExit: function () {
			console.log('App Exit')
		},

		methods: {
			// 检查登录状态
			checkLoginStatus() {
				const token = uni.getStorageSync('token')
				const userInfo = uni.getStorageSync('userInfo')

				if (!token || !userInfo) {
					// 未登录，跳转到登录页
					uni.reLaunch({
						url: '/pages/login/index'
					})
				}
			}
		}
	}
</script>

<style>
	/* 全局样式 */
	page {
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
	}

	/* 通用布局类 */
	.uni-row {
		flex-direction: row;
	}

	.uni-column {
		flex-direction: column;
	}

	.uni-center {
		align-items: center;
		justify-content: center;
	}

	.uni-space-between {
		justify-content: space-between;
	}

	.uni-space-around {
		justify-content: space-around;
	}

	/* 通用文本类 */
	.text-primary {
		color: #007AFF;
	}

	.text-success {
		color: #34C759;
	}

	.text-warning {
		color: #FF9500;
	}

	.text-danger {
		color: #FF3B30;
	}

	.text-muted {
		color: #8E8E93;
	}

	.text-center {
		text-align: center;
	}

	.text-right {
		text-align: right;
	}

	/* 通用间距类 */
	.mt-10 { margin-top: 10rpx; }
	.mt-20 { margin-top: 20rpx; }
	.mt-30 { margin-top: 30rpx; }

	.mb-10 { margin-bottom: 10rpx; }
	.mb-20 { margin-bottom: 20rpx; }
	.mb-30 { margin-bottom: 30rpx; }

	.ml-10 { margin-left: 10rpx; }
	.ml-20 { margin-left: 20rpx; }
	.ml-30 { margin-left: 30rpx; }

	.mr-10 { margin-right: 10rpx; }
	.mr-20 { margin-right: 20rpx; }
	.mr-30 { margin-right: 30rpx; }

	.pt-10 { padding-top: 10rpx; }
	.pt-20 { padding-top: 20rpx; }
	.pt-30 { padding-top: 30rpx; }

	.pb-10 { padding-bottom: 10rpx; }
	.pb-20 { padding-bottom: 20rpx; }
	.pb-30 { padding-bottom: 30rpx; }

	.pl-10 { padding-left: 10rpx; }
	.pl-20 { padding-left: 20rpx; }
	.pl-30 { padding-left: 30rpx; }

	.pr-10 { padding-right: 10rpx; }
	.pr-20 { padding-right: 20rpx; }
	.pr-30 { padding-right: 30rpx; }

	/* 通用按钮样式 */
	.btn {
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		padding: 20rpx 40rpx;
		text-align: center;
		cursor: pointer;
	}

	.btn-primary {
		background-color: #007AFF;
		color: white;
	}

	.btn-success {
		background-color: #34C759;
		color: white;
	}

	.btn-warning {
		background-color: #FF9500;
		color: white;
	}

	.btn-danger {
		background-color: #FF3B30;
		color: white;
	}

	.btn-outline {
		background-color: transparent;
		border: 2rpx solid #007AFF;
		color: #007AFF;
	}

	/* 卡片样式 */
	.card {
		background: white;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.card-header {
		padding: 30rpx;
		border-bottom: 2rpx solid #f0f0f0;
	}

	.card-body {
		padding: 30rpx;
	}

	.card-footer {
		padding: 30rpx;
		border-top: 2rpx solid #f0f0f0;
		background: #f8f8f8;
	}

	/* 列表样式 */
	.list-item {
		background: white;
		padding: 30rpx;
		border-bottom: 2rpx solid #f0f0f0;
		display: flex;
		align-items: center;
	}

	.list-item:last-child {
		border-bottom: none;
	}

	/* 徽章样式 */
	.badge {
		display: inline-block;
		padding: 4rpx 12rpx;
		font-size: 20rpx;
		border-radius: 12rpx;
		color: white;
		text-align: center;
		min-width: 24rpx;
	}

	.badge-primary {
		background-color: #007AFF;
	}

	.badge-success {
		background-color: #34C759;
	}

	.badge-warning {
		background-color: #FF9500;
	}

	.badge-danger {
		background-color: #FF3B30;
	}
</style>