# 校园餐智慧食堂供应商端API服务

## 项目简介

这是校园餐智慧食堂供应商端的后端API服务，基于Node.js + Express + MySQL开发，为微信小程序提供完整的数据接口支持。

## 技术栈

- **Node.js**: 18.x+
- **Express**: 4.x
- **MySQL**: 8.0+
- **JWT**: 用户认证
- **Multer + Sharp**: 文件上传和图片处理
- **bcryptjs**: 密码加密

## 项目结构

```
backend/
├── config/
│   └── database.js          # 数据库配置
├── middleware/
│   ├── auth.js              # 认证中间件
│   └── errorHandler.js      # 错误处理中间件
├── routes/
│   ├── auth.js              # 认证相关路由
│   ├── supplier.js          # 供应商信息路由
│   ├── orders.js            # 订单管理路由
│   ├── products.js          # 产品管理路由
│   ├── schools.js           # 学校管理路由
│   └── upload.js            # 文件上传路由
├── database/
│   └── init.sql             # 数据库初始化脚本
├── uploads/                 # 文件上传目录
├── .env                     # 环境配置文件
├── app.js                   # 应用入口文件
├── package.json             # 项目依赖
└── README.md               # 项目说明
```

## 快速开始

### 1. 环境要求

- Node.js 18.x 或更高版本
- MySQL 8.0 或更高版本
- npm 或 yarn

### 2. 安装依赖

```bash
cd backend
npm install
```

### 3. 数据库配置

#### 3.1 连接现有数据库

项目已配置连接到您的数据库：
- **主机**: **************
- **用户名**: StudentsCMSSP
- **密码**: Xg2LS44Cyz5Zt8.
- **数据库**: StudentsCMSSP

#### 3.2 初始化数据表

执行数据库初始化脚本：

```bash
# 方法1：使用MySQL命令行
mysql -h ************** -u StudentsCMSSP -p StudentsCMSSP < database/init.sql

# 方法2：使用MySQL Workbench或其他工具
# 连接到数据库后，执行 database/init.sql 文件中的SQL语句
```

### 4. 环境配置

检查并修改 `.env` 文件中的配置：

```env
# 数据库配置（已配置）
DB_HOST=**************
DB_USER=StudentsCMSSP
DB_PASSWORD=Xg2LS44Cyz5Zt8.
DB_NAME=StudentsCMSSP
DB_PORT=3306

# 服务器配置
PORT=3000
NODE_ENV=development

# JWT配置（请修改为安全的密钥）
JWT_SECRET=your_jwt_secret_key_here_change_in_production
JWT_EXPIRES_IN=7d

# 微信小程序配置（需要配置）
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

### 5. 启动服务

```bash
# 开发模式（推荐）
npm run dev

# 生产模式
npm start
```

服务启动后，访问 http://localhost:3000/health 检查服务状态。

## API接口文档

### 认证接口

#### 供应商登录
```
POST /api/auth/login
Content-Type: application/json

{
  "username": "supplier001",
  "password": "123456"
}
```

#### 微信登录
```
POST /api/auth/wx_login
Content-Type: application/json

{
  "code": "微信登录code"
}
```

#### 绑定微信账号
```
POST /api/auth/bind_account
Content-Type: application/json

{
  "openid": "微信openid",
  "username": "supplier001",
  "password": "123456"
}
```

### 供应商接口

#### 获取供应商信息
```
GET /api/supplier/profile
Authorization: Bearer {token}
```

#### 更新供应商信息
```
PUT /api/supplier/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "新的供应商名称",
  "phone": "***********",
  "email": "<EMAIL>"
}
```

#### 获取统计数据
```
GET /api/supplier/statistics
Authorization: Bearer {token}
```

### 订单接口

#### 获取订单列表
```
GET /api/supplier/orders?page=1&per_page=10&status=1
Authorization: Bearer {token}
```

#### 获取订单详情
```
GET /api/supplier/orders/{id}
Authorization: Bearer {token}
```

#### 接受订单
```
POST /api/supplier/orders/{id}/accept
Authorization: Bearer {token}
```

#### 拒绝订单
```
POST /api/supplier/orders/{id}/reject
Authorization: Bearer {token}
Content-Type: application/json

{
  "reason": "拒绝原因"
}
```

### 产品接口

#### 获取产品列表
```
GET /api/supplier/products?page=1&per_page=10
Authorization: Bearer {token}
```

#### 添加产品
```
POST /api/supplier/products
Authorization: Bearer {token}
Content-Type: application/json

{
  "product_name": "产品名称",
  "product_code": "产品编码",
  "specification": "规格说明",
  "price": 10.50,
  "unit": "件"
}
```

### 文件上传接口

#### 上传头像
```
POST /api/upload/avatar
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: 图片文件
```

#### 上传产品图片
```
POST /api/upload/product
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: 图片文件
```

## 测试账号

数据库初始化后，可以使用以下测试账号：

```
用户名: supplier001
密码: 123456
供应商: 绿色农产品供应商

用户名: supplier002  
密码: 123456
供应商: 优质肉类供应商
```

## 开发调试

### 1. 查看日志

服务运行时会在控制台输出详细的日志信息，包括：
- 数据库连接状态
- API请求日志
- 错误信息

### 2. 数据库调试

可以直接连接数据库查看数据：

```bash
mysql -h ************** -u StudentsCMSSP -p StudentsCMSSP
```

### 3. API测试

推荐使用以下工具测试API：
- **Postman**: 图形化API测试工具
- **curl**: 命令行工具
- **VS Code REST Client**: VS Code插件

示例curl命令：

```bash
# 登录获取token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"supplier001","password":"123456"}'

# 使用token访问API
curl -X GET http://localhost:3000/api/supplier/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 部署说明

### 1. 生产环境配置

修改 `.env` 文件：

```env
NODE_ENV=production
JWT_SECRET=生产环境的安全密钥
PORT=3000
```

### 2. 使用PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start app.js --name "campus-canteen-api"

# 查看状态
pm2 status

# 查看日志
pm2 logs campus-canteen-api
```

### 3. 使用Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 常见问题

### Q: 数据库连接失败？
A: 检查以下几点：
- 网络连接是否正常
- 数据库服务是否运行
- 用户名密码是否正确
- 防火墙设置

### Q: JWT token无效？
A: 检查：
- token是否过期
- JWT_SECRET是否正确
- 请求头格式是否正确

### Q: 文件上传失败？
A: 检查：
- 文件大小是否超限
- 文件格式是否支持
- uploads目录权限

### Q: 图片无法显示？
A: 检查：
- 静态文件服务是否正常
- 图片路径是否正确
- 文件是否存在

## 技术支持

如有问题，请联系开发团队或查看项目文档。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础API功能
- 支持用户认证
- 支持订单和产品管理
- 支持文件上传
